FROM 192.168.31.199:8000/algo/vita-cuda12.8.1-ubuntu22.04-cmorl-runtime:v0.1

ENV ROS_DISTRO=humble
ENV DEBIAN_FRONTEND=noninteractive

RUN echo "deb [ signed-by=/usr/share/keyrings/ros2-latest-archive-keyring.gpg ] http://packages.ros.org/ros2/ubuntu jammy main" > /etc/apt/sources.list.d/ros2-latest.list
# setup keys
RUN set -eux; \
       key='C1CF6E31E6BADE8868B172B4F42ED6FBAB17C654'; \
       export GNUPGHOME="$(mktemp -d)"; \
       gpg --batch --keyserver keyserver.ubuntu.com --recv-keys "$key"; \
       mkdir -p /usr/share/keyrings; \
       gpg --batch --export "$key" > /usr/share/keyrings/ros2-latest-archive-keyring.gpg; \
       gpgconf --kill all; \
       rm -rf "$GNUPGHOME"
RUN apt-get update && apt-get install --no-install-recommends -y \
    build-essential \
    dirmngr \
    gnupg2 \
    git \
    libgl1 libegl-dev \
    python3.10 \
    python3-pip \
    python3-rosdep \
    && pip install uv \
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update && apt-get install -y --no-install-recommends \
    ros-humble-ros-core=0.10.0-1* \
    && rm -rf /var/lib/apt/lists/*

RUN rosdep init && \
    rosdep update --rosdistro $ROS_DISTRO
  
RUN apt-get update && apt-get install -y --no-install-recommends \
    ros-humble-ros-base=0.10.0-1* \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /root/vitasim_cloud
RUN mkdir -p mcap models
COPY src/robot models/
ENV MUJOCO_GL=EGL
COPY install install

COPY tools/offscreen offscreen
WORKDIR /root/vitasim_cloud/offscreen
RUN uv sync
