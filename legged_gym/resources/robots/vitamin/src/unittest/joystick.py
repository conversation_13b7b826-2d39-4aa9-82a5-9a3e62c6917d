import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Joy
import pygame
import sys
import traceback


class VirtualJoy(Node):
    def __init__(self):
        super().__init__("virtual_joy")
        self.publisher_ = self.create_publisher(Joy, "/joy", 10)

        # 移除定时器，改为事件驱动
        self.prev_keys_state = {}  # 存储上一次的按键状态
        self.prev_axes = [0.0] * 8  # 存储上一次的轴状态
        self.prev_buttons = [0] * 12  # 存储上一次的按钮状态

        # 初始化 PyGame
        pygame.init()
        self.screen = pygame.display.set_mode((800, 600))
        pygame.display.set_caption("ROS2 Virtual JoyStick Simulator")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.SysFont("Arial", 16)

        # 模式设置 - 默认为基础模式
        self.advanced_mode = False

        # 当前摇杆和按钮状态
        self.axes = [0.0] * 8
        self.buttons = [0] * 12
        self.last_msg_time = 0

        # 按键映射 - 根据对应表更新
        self.key_mapping = {
            # 左摇杆 - 修正为与图表一致
            pygame.K_a: (0, 1.0),  # 左摇杆左 (左: 1)
            pygame.K_d: (0, -1.0),  # 左摇杆右 (右: -1)
            pygame.K_w: (1, 1.0),  # 左摇杆上 (上: 1)
            pygame.K_s: (1, -1.0),  # 左摇杆下 (下: -1)
            # 右摇杆 - 修正为与图表一致
            pygame.K_LEFT: (3, 1.0),  # 右摇杆左 (左: 1)
            pygame.K_RIGHT: (3, -1.0),  # 右摇杆右 (右: -1)
            pygame.K_UP: (4, 1.0),  # 右摇杆上 (上: 1)
            pygame.K_DOWN: (4, -1.0),  # 右摇杆下 (下: -1)
            # 方向键 (D-pad) - 修正为与图表一致
            pygame.K_KP4: (6, 1.0),  # 方向键左 (左: 1)
            pygame.K_KP6: (6, -1.0),  # 方向键右 (右: -1)
            pygame.K_KP8: (7, 1.0),  # 方向键上 (上: 1)
            pygame.K_KP2: (7, -1.0),  # 方向键下 (下: -1)
        }

        # 扳机键映射 - 特殊处理
        self.trigger_mapping = {
            pygame.K_q: 2,  # LT
            pygame.K_e: 5,  # RT
        }

        # 按钮映射 - 根据对应表更新
        self.button_mapping = {
            pygame.K_SPACE: 0,  # A
            pygame.K_RETURN: 1,  # B
            pygame.K_x: 2,  # X
            pygame.K_y: 3,  # Y
            pygame.K_1: 4,  # LB
            pygame.K_3: 5,  # RB
            pygame.K_BACKSPACE: 6,  # SELECT
            pygame.K_TAB: 7,  # START
            pygame.K_z: 8,  # 左摇杆下压
            pygame.K_c: 9,  # 右摇杆下压
        }

        # 按钮名称
        self.button_names = {
            0: "A",
            1: "B",
            2: "X",
            3: "Y",
            4: "LB",
            5: "RB",
            6: "SELECT",
            7: "START",
            8: "L3",
            9: "R3",
        }

        # 摇杆名称
        self.axis_names = {
            0: "Left Stick X",
            1: "Left Stick Y",
            2: "LT",
            3: "Right Stick X",
            4: "Right Stick Y",
            5: "RT",
            6: "D-Pad X",
            7: "D-Pad Y",
        }

        # 按键说明
        self.key_instructions = {
            0: "Space",
            1: "Enter",
            2: "x key",
            3: "y key",
            4: "1 key",
            5: "3 key",
            6: "Backspace",
            7: "Tab",
            8: "z key",
            9: "c key",
            "left_stick": "WASD",
            "right_stick": "Arrow Keys",
            "dpad": "Numpad 8/2/4/6",
            "lt": "q key",
            "rt": "e key",
        }

        self.get_logger().info("Virtual JoyStick Simulator started")
        self.get_logger().info("Key Mapping:")
        self.get_logger().info("WASD: Left Stick (Left: 1, Right: -1, Up: 1, Down: -1)")
        self.get_logger().info(
            "Arrow Keys: Right Stick (Left: 1, Right: -1, Up: 1, Down: -1)"
        )
        self.get_logger().info(
            "Numpad 8/2/4/6: D-Pad (Left: 1, Right: -1, Up: 1, Down: -1)"
        )
        self.get_logger().info("Q: LT (press: -1, release: 0)")
        self.get_logger().info("E: RT (press: -1, release: 0)")
        self.get_logger().info("1: LB, 3: RB")
        self.get_logger().info("Space: A, Enter: B, X: X, Y: Y")
        self.get_logger().info("Backspace: SELECT, Tab: START")
        self.get_logger().info("Z: L3, C: R3")
        self.get_logger().info("Press M to toggle between Basic and Advanced mode")

        # 发送初始状态消息
        try:
            self.send_joy_msg(force=True)
        except Exception as e:
            self.get_logger().error(f"Error in initial message: {e}")
            traceback.print_exc()

    def send_joy_msg(self, force=False):
        try:
            joy_msg = Joy()
            joy_msg.header.stamp = self.get_clock().now().to_msg()

            # 创建临时状态用于检测变化
            temp_axes = [0.0] * 8  # 确保长度为8
            temp_buttons = [0] * 12  # 确保长度为12

            # 复制当前状态到临时变量
            for i in range(min(len(self.axes), len(temp_axes))):
                temp_axes[i] = self.axes[i]

            for i in range(min(len(self.buttons), len(temp_buttons))):
                temp_buttons[i] = self.buttons[i]

            keys = pygame.key.get_pressed()

            # 存储当前按键状态，用于检测变化
            current_keys_state = {}
            for key in (
                list(self.key_mapping.keys())
                + list(self.trigger_mapping.keys())
                + list(self.button_mapping.keys())
            ):
                current_keys_state[key] = keys[key]

            # 重置轴和按钮状态
            for i in range(len(self.axes)):
                # 跳过LT和RT，它们有特殊处理
                if i != 2 and i != 5:
                    self.axes[i] = 0.0

            for i in range(len(self.buttons)):
                self.buttons[i] = 0

            # 更新轴状态
            for key, (axis, value) in self.key_mapping.items():
                if keys[key]:
                    if 0 <= axis < len(self.axes):  # 检查索引是否有效
                        self.axes[axis] = value
                    else:
                        self.get_logger().error(f"Invalid axis index: {axis}")

            # 特殊处理扳机键 - 按下为-1，松开为0
            for key, axis in self.trigger_mapping.items():
                if 0 <= axis < len(self.axes):  # 检查索引是否有效
                    if keys[key]:
                        self.axes[axis] = -1.0
                    else:
                        self.axes[axis] = 0.0
                else:
                    self.get_logger().error(f"Invalid trigger axis index: {axis}")

            # 更新按钮状态
            for key, button in self.button_mapping.items():
                if keys[key]:
                    if 0 <= button < len(self.buttons):  # 检查索引是否有效
                        self.buttons[button] = 1
                    else:
                        self.get_logger().error(f"Invalid button index: {button}")

            # 更新临时状态
            for i in range(min(len(self.axes), len(temp_axes))):
                temp_axes[i] = self.axes[i]

            for i in range(min(len(self.buttons), len(temp_buttons))):
                temp_buttons[i] = self.buttons[i]

            # 检查是否有状态变化或强制发送
            state_changed = False

            # 检查轴状态变化
            for i in range(len(temp_axes)):
                if (
                    i < len(self.prev_axes)
                    and abs(temp_axes[i] - self.prev_axes[i]) > 0.01
                ):  # 允许小误差
                    state_changed = True
                    break

            # 检查按钮状态变化
            if not state_changed:
                for i in range(len(temp_buttons)):
                    if (
                        i < len(self.prev_buttons)
                        and temp_buttons[i] != self.prev_buttons[i]
                    ):
                        state_changed = True
                        break

            # 如果状态变化或强制发送，则发布消息
            if state_changed or force:
                # 确保消息格式正确
                joy_msg.axes = temp_axes
                joy_msg.buttons = temp_buttons

                self.publisher_.publish(joy_msg)
                self.last_msg_time = pygame.time.get_ticks()

                # 更新上一次的状态
                self.prev_axes = temp_axes.copy()
                self.prev_buttons = temp_buttons.copy()
                self.prev_keys_state = current_keys_state.copy()

                # 记录日志
                if force:
                    self.get_logger().debug("Sending initial joy message")
                else:
                    self.get_logger().debug("Sending joy message due to state change")

            # 无论是否发送消息，都更新当前状态用于显示
            self.axes = temp_axes.copy()
            self.buttons = temp_buttons.copy()

        except Exception as e:
            self.get_logger().error(f"Error in send_joy_msg: {e}")
            traceback.print_exc()

    def draw_joystick(self, x, y, name, axis_x, axis_y, instructions):
        try:
            # 绘制摇杆背景
            pygame.draw.circle(self.screen, (100, 100, 100), (x, y), 50)

            # 绘制摇杆位置 - 修正动画显示方向
            # 对于所有摇杆，使用相同的逻辑：正值向左/上，负值向右/下
            stick_x = x - axis_x * 40  # 左为正(1)，右为负(-1)
            stick_y = y - axis_y * 40  # 上为正(1)，下为负(-1)
            pygame.draw.circle(self.screen, (200, 200, 200), (stick_x, stick_y), 20)

            # 绘制摇杆名称
            text = self.font.render(name, True, (255, 255, 255))
            text_rect = text.get_rect(center=(x, y + 70))
            self.screen.blit(text, text_rect)

            # 绘制按键说明
            inst_text = self.font.render(instructions, True, (200, 200, 200))
            inst_rect = inst_text.get_rect(center=(x, y + 90))
            self.screen.blit(inst_text, inst_rect)

        except Exception as e:
            self.get_logger().error(f"Error in draw_joystick: {e}")
            traceback.print_exc()

    def draw_button(self, x, y, name, state, instructions):
        try:
            color = (200, 50, 50) if state else (100, 100, 100)
            pygame.draw.circle(self.screen, color, (x, y), 20)
            text = self.font.render(name, True, (255, 255, 255))
            text_rect = text.get_rect(center=(x, y))
            self.screen.blit(text, text_rect)

            # 绘制按键说明
            inst_text = self.font.render(instructions, True, (200, 200, 200))
            inst_rect = inst_text.get_rect(center=(x, y + 25))
            self.screen.blit(inst_text, inst_rect)
        except Exception as e:
            self.get_logger().error(f"Error in draw_button: {e}")
            traceback.print_exc()

    def draw_trigger(self, x, y, name, value, instructions):
        try:
            # 绘制按钮样式的扳机键
            color = (200, 50, 50) if value < 0 else (100, 100, 100)
            pygame.draw.circle(self.screen, color, (x, y), 20)

            # 绘制扳机键名称
            text = self.font.render(name, True, (255, 255, 255))
            text_rect = text.get_rect(center=(x, y))
            self.screen.blit(text, text_rect)

            # 绘制按键说明
            inst_text = self.font.render(instructions, True, (200, 200, 200))
            inst_rect = inst_text.get_rect(center=(x, y + 25))
            self.screen.blit(inst_text, inst_rect)

        except Exception as e:
            self.get_logger().error(f"Error in draw_trigger: {e}")
            traceback.print_exc()

    def draw_mode_switch(self):
        try:
            # 绘制模式切换按钮
            mode_text = "Advanced Mode" if self.advanced_mode else "Basic Mode"
            mode_color = (100, 200, 100) if self.advanced_mode else (100, 100, 200)
            mode_button = self.font.render(
                f"{mode_text} (Press M to toggle)", True, mode_color
            )
            mode_rect = mode_button.get_rect(center=(400, 90))
            self.screen.blit(mode_button, mode_rect)
        except Exception as e:
            self.get_logger().error(f"Error in draw_mode_switch: {e}")
            traceback.print_exc()

    def draw_status(self):
        try:
            # 绘制标题
            title_font = pygame.font.SysFont("Arial", 24)
            title = title_font.render(
                "ROS2 Virtual JoyStick Simulator", True, (255, 255, 255)
            )
            title_rect = title.get_rect(center=(400, 30))
            self.screen.blit(title, title_rect)

            # 绘制发布状态
            now = pygame.time.get_ticks()
            if now - self.last_msg_time < 100:  # 如果在过去100ms内发布了消息
                status = self.font.render(
                    "Status: Publishing messages to /joy topic", True, (50, 255, 50)
                )
            else:
                status = self.font.render(
                    "Status: Waiting for input", True, (255, 255, 50)
                )
            status_rect = status.get_rect(center=(400, 60))
            self.screen.blit(status, status_rect)

            # 绘制模式切换按钮
            self.draw_mode_switch()

            # 绘制使用说明
            help_text = self.font.render("Press ESC to exit", True, (200, 200, 200))
            help_rect = help_text.get_rect(center=(400, 570))
            self.screen.blit(help_text, help_rect)

            # 绘制控制区域
            self.draw_controls()

            # 绘制当前Joy消息值
            self.draw_joy_values()
        except Exception as e:
            self.get_logger().error(f"Error in draw_status: {e}")
            traceback.print_exc()

    def draw_controls(self):
        try:
            # 计算布局 - 整体左移
            center_x = 250
            left_x = 100
            right_x = 400

            # 基础模式下的行间距更大
            if self.advanced_mode:
                row1_y = 150  # 摇杆行
                row2_y = 280  # 按钮第一行
                row3_y = 360  # 按钮第二行
                row4_y = 440  # 按钮第三行
            else:
                row1_y = 150  # 摇杆行
                row2_y = 300  # 按钮第一行
                row3_y = 400  # 按钮第二行
                row4_y = 500  # 按钮第三行

            # 绘制摇杆行
            self.draw_joystick(
                left_x,
                row1_y,
                "Left Stick",
                self.axes[0],
                self.axes[1],
                self.key_instructions["left_stick"],
            )

            # 高级模式才显示D-Pad
            if self.advanced_mode:
                self.draw_joystick(
                    center_x,
                    row1_y,
                    "D-Pad",
                    self.axes[6],
                    self.axes[7],
                    self.key_instructions["dpad"],
                )

            self.draw_joystick(
                right_x,
                row1_y,
                "Right Stick",
                self.axes[3],
                self.axes[4],
                self.key_instructions["right_stick"],
            )

            # 按钮间距
            button_spacing = 75

            # 绘制按钮第一行
            self.draw_button(
                left_x, row2_y, "A", self.buttons[0], self.key_instructions[0]
            )
            self.draw_button(
                left_x + button_spacing,
                row2_y,
                "B",
                self.buttons[1],
                self.key_instructions[1],
            )
            self.draw_button(
                center_x + button_spacing,
                row2_y,
                "X",
                self.buttons[2],
                self.key_instructions[2],
            )
            self.draw_button(
                center_x + 2 * button_spacing,
                row2_y,
                "Y",
                self.buttons[3],
                self.key_instructions[3],
            )

            # 绘制按钮第二行
            self.draw_button(
                left_x, row3_y, "LB", self.buttons[4], self.key_instructions[4]
            )
            self.draw_button(
                left_x + button_spacing,
                row3_y,
                "RB",
                self.buttons[5],
                self.key_instructions[5],
            )
            self.draw_button(
                center_x + button_spacing,
                row3_y,
                "SEL",
                self.buttons[6],
                self.key_instructions[6],
            )
            self.draw_button(
                center_x + 2 * button_spacing,
                row3_y,
                "STA",
                self.buttons[7],
                self.key_instructions[7],
            )

            # 绘制按钮第三行 - 包括LT和RT
            # 高级模式才显示L3和R3
            if self.advanced_mode:
                self.draw_button(
                    left_x, row4_y, "L3", self.buttons[8], self.key_instructions[8]
                )
                self.draw_button(
                    left_x + button_spacing,
                    row4_y,
                    "R3",
                    self.buttons[9],
                    self.key_instructions[9],
                )
                self.draw_trigger(
                    center_x + button_spacing,
                    row4_y,
                    "LT",
                    self.axes[2],
                    self.key_instructions["lt"],
                )
                self.draw_trigger(
                    center_x + 2 * button_spacing,
                    row4_y,
                    "RT",
                    self.axes[5],
                    self.key_instructions["rt"],
                )
            else:
                # 基础模式下LT和RT放在左侧
                self.draw_trigger(
                    left_x, row4_y, "LT", self.axes[2], self.key_instructions["lt"]
                )
                self.draw_trigger(
                    left_x + button_spacing,
                    row4_y,
                    "RT",
                    self.axes[5],
                    self.key_instructions["rt"],
                )
        except Exception as e:
            self.get_logger().error(f"Error in draw_controls: {e}")
            traceback.print_exc()

    def draw_joy_values(self):
        try:
            # 绘制当前Joy消息值的标题
            values_title = self.font.render(
                "Current Joy Message Values:", True, (255, 255, 255)
            )
            values_rect = values_title.get_rect()
            values_rect.topleft = (550, 150)
            self.screen.blit(values_title, values_rect)

            # 绘制axes值
            y_pos = 180
            for i in range(8):
                if i in self.axis_names:
                    # 基础模式下只显示常用的轴
                    if not self.advanced_mode and (i == 6 or i == 7):
                        continue
                    text = self.font.render(
                        f"axes[{i}]: {self.axes[i]:.2f} - {self.axis_names[i]}",
                        True,
                        (200, 200, 200),
                    )
                    self.screen.blit(text, (550, y_pos))
                    y_pos += 20

            # 绘制buttons值
            y_pos += 10
            for i in range(10):
                if i in self.button_names:
                    # 基础模式下只显示常用的按钮
                    if not self.advanced_mode and (i == 8 or i == 9):
                        continue
                    text = self.font.render(
                        f"buttons[{i}]: {self.buttons[i]} - {self.button_names[i]}",
                        True,
                        (200, 200, 200),
                    )
                    self.screen.blit(text, (550, y_pos))
                    y_pos += 20
        except Exception as e:
            self.get_logger().error(f"Error in draw_joy_values: {e}")
            traceback.print_exc()

    def run(self):
        running = True
        while running and rclpy.ok():
            try:
                self.screen.fill((30, 30, 50))  # 深蓝色背景

                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        running = False
                    elif event.type == pygame.KEYDOWN:
                        if event.key == pygame.K_ESCAPE:
                            running = False
                        elif event.key == pygame.K_m:
                            # 切换模式
                            self.advanced_mode = not self.advanced_mode
                            # 模式切换后发送一次复位消息
                            self.send_joy_msg(force=True)
                            mode_text = "Advanced" if self.advanced_mode else "Basic"
                            self.get_logger().info(f"Switched to {mode_text} Mode")
                    elif event.type == pygame.KEYUP:
                        # 按键释放时也检查状态变化
                        self.send_joy_msg()

                # 检查按键状态变化并发送消息
                self.send_joy_msg()

                # 绘制界面
                self.draw_status()
                pygame.display.flip()

                # 处理ROS消息
                rclpy.spin_once(self, timeout_sec=0)
                self.clock.tick(30)
            except Exception as e:
                self.get_logger().error(f"Error in run loop: {e}")
                traceback.print_exc()
                # 继续运行，不要因为一次错误就退出
                pygame.time.delay(100)  # 短暂延迟，避免错误消息刷屏

        pygame.quit()
        self.get_logger().info("Virtual JoyStick Simulator closed")


def main(args=None):
    rclpy.init(args=args)
    try:
        node = VirtualJoy()
        node.run()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()
    finally:
        rclpy.shutdown()


if __name__ == "__main__":
    main()
