// Copyright 2025 VitaDynamics Limited

#include "vita_sim/mj_utils/agent.h"

#include "simulate/array_safety.h"

namespace mju = ::mujoco::sample_util;
namespace vita_sim {

UserGeom::UserGeom(const std::string &modelpath, const float *color) {
  mjModel *m = mj_loadXML(modelpath.c_str(), nullptr, nullptr, 1024);
  if (!m) {
    throw std::runtime_error("Failed to load model from " + modelpath);
  }
  model = std::shared_ptr<mjModel>(m, mj_deleteModel);
  data = std::shared_ptr<mjData>(mj_makeData(model.get()), mj_deleteData);
  opt = std::make_shared<mjvOption>();
  mjv_defaultOption(opt.get());
  pert = std::make_shared<mjvPerturb>();
  mjv_defaultPerturb(pert.get());
  nq_ = model->nq;
  color_setting.reserve(4);

  // RGB
  for (int i = 0; i < 3; ++i) {
    color_setting.push_back(color[i] / 255.0f);
  }
  // alpha
  color_setting.push_back(color[3]);
}

void UserGeom::AddState2Hist(int index) {
  if (hist_states.empty()) {
    return;
  }
  mjtNum *state = &hist_states[index];
  mj_getState(model.get(), data.get(), state, mjSTATE_INTEGRATION);
}

FigData::FigData(const std::string &fig_name,
                 const std::vector<std::string> &legend_names) {
  fig = std::make_shared<mjvFigure>();
  mjv_defaultFigure(fig.get());
  mju::strcpy_arr(fig->title, fig_name.c_str());
  mju::strcpy_arr(fig->yformat, "%.2f");
  mju::strcpy_arr(fig->xformat, "%.2f");
  fig->figurergba[0] = 0.1f;
  fig->figurergba[3] = 0.5f;
  fig->gridsize[0] = 5;
  fig->gridsize[1] = 5;
  fig->range[0][0] = 0.0;
  fig->range[0][1] = 8.0;
  fig->range[1][0] = -3.0f;
  fig->range[1][1] = 3.0f;

  for (size_t i = 0; i < legend_names.size(); ++i) {
    mju::strcpy_arr(fig->linename[i], legend_names[i].c_str());
    // color of the first point on each line , in RGB / 255
    fig->linergb[i][0] = kFigLineColors[i][0] / 255.0f;
    fig->linergb[i][1] = kFigLineColors[i][1] / 255.0f;
    fig->linergb[i][2] = kFigLineColors[i][2] / 255.0f;
  }
  for (size_t i = legend_names.size(); i < mjMAXLINE; ++i) {
    // set color of the following points same as the first ones
    fig->linergb[i][0] = fig->linergb[i - legend_names.size()][0];
    fig->linergb[i][1] = fig->linergb[i - legend_names.size()][1];
    fig->linergb[i][2] = fig->linergb[i - legend_names.size()][2];
  }
  line_data.resize(legend_names.size());
}

Agent::Agent() {
  low_cmd_que_ = std::make_unique<MessageQueue<LowCmdMsg>>();
  rt_low_state_que_ = std::make_unique<MessageQueue<LowStateMsg>>();
  cur_lowcmd_ = std::make_shared<LowCmdMsg>();
  rt_low_state_ = std::make_shared<LowStateMsg>();
  InitPlot();
}

// graphical user interface elements for agent and task
void Agent::MakeGUI(mjUI &ui) {
  // ----- agent ----- //
  mjuiDef def_agent[] = {
      {mjITEM_SECTION, "Agent", 1, nullptr, "AP", 0},
      {mjITEM_SEPARATOR, "Agent Options", 1, nullptr, "", 0},           // 0
      {mjITEM_CHECKINT, "Low State", 2, &is_show_custom_figs_, "", 0},  // 1
      {mjITEM_CHECKINT, "Ctrl Tau", 2, &is_show_tau_figs_, "", 0},      // 2
      {mjITEM_CHECKINT, "Shadow Mode", 2, &shadow_mode_, "", 0},        // 3
      {mjITEM_CHECKINT, "Depth Cam", 2, &depth_cam_, "", 0},            // 4
      {mjITEM_SEPARATOR, "History", 1, nullptr, "", 0},                 // 5
      {mjITEM_SLIDERINT, "", 5, &scrub_index_, "0 0", 0},               // 6
      {mjITEM_SEPARATOR, "Realtime Viualization", 1, nullptr, "", 0},   // 7
      {mjITEM_CHECKINT, "Realtime State", 2, &is_rt_vis_, "", 0},       // 8
      {mjITEM_CHECKINT, "Tau EST", 2, &is_tau_est_, "", 0},             // 9
      {mjITEM_SEPARATOR, "Sim Test", 1, nullptr, "", 0},                // 10
      {mjITEM_SLIDERNUM, "Load (kg)", 2, &load_weight_, "0 10", 0},     // 11
      {mjITEM_END, "", 0, nullptr, "", 0}};

  // add agent
  mjui_add(&ui, def_agent);
}

void Agent::InitUserGeom(const std::string &model_path,
                         std::shared_ptr<UserGeom> &geom, const float *color) {
  try {
    geom = std::make_shared<UserGeom>(model_path, color);
    geom->opt->flags[mjtVisFlag::mjVIS_TRANSPARENT] = true;
    // 禁用关节动力学（关节力矩的限位等）
    geom->model->opt.disableflags |= mjDSBL_ACTUATION;
  } catch (const std::exception &e) {
    std::cerr << "Error initializing user geometry: " << e.what() << std::endl;
  }
}

void Agent::UpdateGhostQpos(mjData *data) {
  if (!ghost_geom_) {
    return;  // 如果ghost_geom_为nullptr，直接返回
  }

  /**
   * qpos[0:19]与vit00的对应关系
   * [0:3]  joint_fixed_world   {x, y, z}
   * [3:7]  joint_fixed_world   {w, x,  y, z}
   * [7]    FR_hip_joint        angel
   * [8]    FR_thigh_joint      angel
   * [9]    FR_calf_joint       angel
   * [10]   FL_hip_joint        angel
   * [11]   FL_thigh_joint      angel
   * [12]   FL_calf_joint       angel
   * [13]   RR_hip_joint        angel
   * [14]   RR_thigh_joint      angel
   * [15]   RR_calf_joint       angel
   * [16]   RL_hip_joint        angel
   * [17]   RL_thigh_joint      angel
   * [18]   RL_calf_joint       angel
   *  */

  std::memcpy(ghost_geom_->data->qpos, data->qpos,
              sizeof(data->qpos) * ghost_geom_->nq_);

  if (cur_lowcmd_ != nullptr) {
    auto motor_cmd = cur_lowcmd_->motor_cmd;
    for (int i = kPos + kQuat; i < ghost_geom_->nq_; ++i) {
      ghost_geom_->data->qpos[i] = motor_cmd[i - kPos - kQuat].q;
    }
  }

  mj_forward(ghost_geom_->model.get(), ghost_geom_->data.get());
}

void Agent::UpdateRtVisQpos(mjData *data) {
  if (!rt_vis_geom_) {
    return;  // 如果rt_vis_geom_为nullptr，直接返回
  }

  if (rt_low_state_ != nullptr) {
    auto low_state = rt_low_state_->motor_state;
    auto imu_quat = rt_low_state_->imu_state.quaternion;

    std::memcpy(rt_vis_geom_->data->qpos, data->qpos,
                sizeof(data->qpos[0]) * kPos);

    // use quaternion from real time imu data
    for (int i = kPos; i < kPos + kQuat; ++i) {
      rt_vis_geom_->data->qpos[i] = imu_quat[i - kPos];
    }
    // use motor state from real time low state
    for (int i = kPos + kQuat; i < rt_vis_geom_->nq_; ++i) {
      rt_vis_geom_->data->qpos[i] = low_state[i - kPos - kQuat].q;
    }
  }

  mj_forward(rt_vis_geom_->model.get(), rt_vis_geom_->data.get());
}

void Agent::ShowActuatorCtrlValue(mjvScene *scn, mjModel *mj_m, mjData *mj_d) {
  // find first vis geom of actuator
  int vis_geom_start_id = 0;
  auto first_actuator_name = mj_id2name(mj_m, mjtObj::mjOBJ_ACTUATOR, 0);
  for (int i = 0; i < mj_m->ngeom; ++i) {
    if (scn->geoms[i].label != first_actuator_name) continue;
    vis_geom_start_id = i;
    break;
  }
  // apply ctrl value to the label
  for (int i = 0; i < mj_m->nu; ++i) {
    auto ctrl_value = mj_d->ctrl[i];
    int geom_idx = vis_geom_start_id + i;

    char original_label[kMaxChar] = {0};
    strncpy(original_label, scn->geoms[geom_idx].label, kMaxChar);

    char new_label[kMaxChar] = {0};
    if (original_label[0] == '\0') {
      snprintf(new_label, kMaxChar, "%.4f", ctrl_value);
    } else {
      snprintf(new_label, kMaxChar, "%.*s: %.4f",
               static_cast<int>(kMaxChar - 10), original_label, ctrl_value);
    }

    strncpy(scn->geoms[geom_idx].label, new_label, kMaxChar);
  }
}

void Agent::InitHistStatesUserGeom(std::shared_ptr<UserGeom> geom) {
  geom->hist_states.clear();
  geom->hist_states.resize(nhistory_ * state_size_);
  mj_getState(geom->model.get(), geom->data.get(), geom->hist_states.data(),
              mjSTATE_INTEGRATION);

  for (int i = 1; i < nhistory_; ++i) {
    mju_copy(&geom->hist_states[i * state_size_], geom->hist_states.data(),
             state_size_);
  }
}

void Agent::InitHistStates(mjModel *mj_model, mjData *mj_data,
                           mjuiSection *sect) {
  // get state size, size of history buffer
  state_size_ = mj_stateSize(mj_model, mjSTATE_INTEGRATION);
  int state_bytes = state_size_ * sizeof(mjtNum);
  int history_length = mjMIN(INT_MAX / state_bytes, hist_states_);
  int history_bytes = mjMIN(state_bytes * history_length, hist_buf_size_);
  nhistory_ = history_bytes / state_bytes;

  // allocate history buffer, reset cursor and UI slider
  history_.clear();
  history_.resize(nhistory_ * state_size_);
  cmd_history_.clear();
  cmd_history_.resize(nhistory_, 0);
  state_history_.clear();
  state_history_.resize(nhistory_, 0);

  history_cursor_ = 0;
  scrub_index_ = 0;

  // fill buffer with initial state
  mj_getState(mj_model, mj_data, history_.data(), mjSTATE_INTEGRATION);

  for (int i = 1; i < nhistory_; ++i) {
    mju_copy(&history_[i * state_size_], history_.data(), state_size_);
  }

  InitHistStatesUserGeom(ghost_geom_);
  InitHistStatesUserGeom(rt_vis_geom_);

  sect->item[6].slider.range[0] = 1 - nhistory_;
  sect->item[6].slider.divisions = nhistory_;
}

void Agent::OnKeyRight(mjModel *mj_model, mjData *mj_data, mjUI *ui, int sec_id,
                       mjuiState *ui_state, mjrContext *con) {
  scrub_index_++;
  is_load_hist_ = true;
  mjui_update(sec_id, -1, ui, ui_state, con);
}

void Agent::OnKeyLeft(mjModel *mj_model, mjData *mj_data, mjUI *ui, int sec_id,
                      mjuiState *ui_state, mjrContext *con) {
  scrub_index_ = mjMAX(scrub_index_ - 1, 1 - nhistory_);
  is_load_hist_ = true;
  mjui_update(sec_id, -1, ui, ui_state, con);
}

void Agent::LoadState(mjModel *mj_model, mjData *mj_data,
                      mjDoubleVec &hist_buf) {
  // get index into circular buffer
  int i = (scrub_index_ + history_cursor_) % nhistory_;
  i = (i + nhistory_) % nhistory_;

  // load state
  mjtNum *state = &hist_buf[i * state_size_];
  mj_setState(mj_model, mj_data, state, mjSTATE_INTEGRATION);

  // call forward dynamics
  mj_forward(mj_model, mj_data);
}

// load state from history buffer
void Agent::LoadScrubState(mjModel *mj_model, mjData *mj_data) {
  LoadState(mj_model, mj_data, history_);
  is_load_hist_ = false;
}

void Agent::LoadUserGeomState(mjvScene *scn, std::shared_ptr<UserGeom> geom) {
  LoadState(geom->model.get(), geom->data.get(), geom->hist_states);
  UpdateGeom(scn, geom);
}

void Agent::AddState2Hist(mjModel *mj_model, mjData *mj_data) {
  if (history_.empty()) {
    return;
  }

  // circular increment of cursor
  history_cursor_ = (history_cursor_ + 1) % nhistory_;

  // add state at cursor
  auto state_index = state_size_ * history_cursor_;
  mjtNum *state = &history_[state_index];
  mj_getState(mj_model, mj_data, state, mjSTATE_INTEGRATION);

  if (cur_lowcmd_) {
    last_cmd_id_ = cur_lowcmd_->cmd_id;
  }
  cmd_history_[history_cursor_] = last_cmd_id_;

  if (is_rt_vis_ && rt_low_state_) {
    last_state_id_ = rt_low_state_->state_id;
  }
  state_history_[history_cursor_] = last_state_id_;

  // add states of user defined geometry
  ghost_geom_->AddState2Hist(state_index);
  rt_vis_geom_->AddState2Hist(state_index);
}

void Agent::UpdateGeom(mjvScene *scn, std::shared_ptr<UserGeom> &geom) {
  auto cur_geom_cnt = scn->ngeom;
  mjv_addGeoms(geom->model.get(), geom->data.get(), geom->opt.get(),
               geom->pert.get(), mjCAT_DYNAMIC, scn);
  for (int i = cur_geom_cnt; i < scn->ngeom; ++i) {
    scn->geoms[i].rgba[0] = geom->color_setting[0];
    scn->geoms[i].rgba[1] = geom->color_setting[1];
    scn->geoms[i].rgba[2] = geom->color_setting[2];
    scn->geoms[i].rgba[3] = geom->color_setting[3];
  }
}

void Agent::ChangeRawHistoryState(mjUI *ui, int range) {
  auto range_left_border = range == 0 ? 0 : 1 - range;
  ui->sect[kSimSectIndex].item[11].slider.range[0] = range_left_border;
  ui->sect[kSimSectIndex].item[11].slider.divisions = range;
}

void Agent::InitPlot() {
  // set figures to default
  std::vector<std::string> fig_names = {"Front Right", "Front Left",
                                        "Rear Right", "Rear Left"};
  std::vector<std::string> legend_names = {"state_hip",  "state_thigh",
                                           "state_calf", "cmd_hip",
                                           "cmd_thigh",  "cmd_calf"};
  std::vector<std::string> tau_names = {"tau_hip", "tau_thigh", "tau_calf"};
  joint_figs_.resize(4);
  for (size_t i = 0; i < fig_names.size(); ++i) {
    joint_figs_[i] = std::make_shared<FigData>(fig_names[i], legend_names);
  }
  tau_figs_.resize(4);
  for (size_t i = 0; i < fig_names.size(); ++i) {
    tau_figs_[i] = std::make_shared<FigData>(fig_names[i], tau_names);
  }

  tau_est_figs_.resize(3);
  for (size_t i = 0; i < tau_names.size(); ++i) {
    tau_est_figs_[i] = std::make_shared<FigData>(tau_names[i], fig_names);
  }
}

// plots - update data
void Agent::PlotUpdateData(std::shared_ptr<FigData> fig_data,
                           const mjtNum &cur_time) {
  // iterate on the 6 lines
  for (size_t i = 0; i < fig_data->line_data.size(); ++i) {
    auto stored_size = fig_data->line_data[i].size();
    int pnt = mjMIN(fig_pnt_, stored_size);
    auto pnt_it = fig_data->line_data[i].rbegin();
    auto pnt_it_end = fig_data->line_data[i].rend();
    // assign data to the line, order is: x, y, x, y...
    for (int j = 0; j < pnt && pnt_it != pnt_it_end; ++j, ++pnt_it) {
      auto [value, time] = *pnt_it;
      // 只绘制当前模型mjData的time之前的点
      // 在找到目标点之前，曲线的index不变化，置为1
      if (cur_time < time && static_cast<size_t>(pnt) < stored_size) {
        j = -1;
        continue;
      }
      fig_data->fig->linedata[i][2 * j] = time - first_cmd_time_;  // axis x
      fig_data->fig->linedata[i][2 * j + 1] = value;               // axis y
    }
    fig_data->fig->linepnt[i] = pnt;
  }
}

void Agent::Plots(const mjData *mj_data, bool is_run) {
  // store each [y, x] of the point
  auto saveData = [this](const float &value, const mjtNum &time,
                         LineQue &line_data) {
    // line buffer is kStorePntFactor x rendered pnt
    if (line_data.size() > fig_pnt_ * kStorePntFactor) {
      line_data.pop_front();
    }
    line_data.push_back({value, time});
  };

  auto setRange = [this](std::shared_ptr<mjvFigure> fig, const mjtNum &time) {
    if (time > fig_range_) {
      fig->range[0][0] = time - fig_range_ - first_cmd_time_;
      fig->range[0][1] = time - first_cmd_time_;
    } else {
      fig->range[0][0] = 0 - first_cmd_time_;
      fig->range[0][1] = fig_range_ - first_cmd_time_;
    }
  };

  // joint q figures
  for (size_t i = 0; i < joint_figs_.size(); ++i) {
    for (int j = 0; j < kJointType; ++j) {
      setRange(joint_figs_[i]->fig, mj_data->time);
      setRange(tau_figs_[i]->fig, mj_data->time);
      // stop adding new data to buffer when simulation is paused
      if (!is_run) continue;
      // save low state from MuJoCo
      saveData(mj_data->sensordata[i * kJointType + j], mj_data->time,
               joint_figs_[i]->line_data[j]);
      // save low cmd from controller
      auto cmd_msg = std::make_shared<LowCmdMsg>();
      if (cur_lowcmd_ != nullptr) cmd_msg = cur_lowcmd_;
      auto &motor_cmd = cmd_msg->motor_cmd;
      saveData(motor_cmd[i * kJointType + j].q, mj_data->time,
               joint_figs_[i]->line_data[j + kJointType]);
      // show tau from MuJoCo
      saveData(mj_data->ctrl[i * kJointType + j], mj_data->time,
               tau_figs_[i]->line_data[j]);
    }
    PlotUpdateData(joint_figs_[i], mj_data->time);
    PlotUpdateData(tau_figs_[i], mj_data->time);
  }

  // tau est figures
  for (size_t i = 0; i < tau_est_figs_.size(); ++i) {
    for (int j = 0; j < kFootNum; ++j) {
      setRange(tau_est_figs_[i]->fig, mj_data->time);
      // stop adding new data to buffer when simulation is paused
      if (!is_run) continue;

      auto rt_low_state = std::make_shared<LowStateMsg>();
      if (rt_low_state_ != nullptr) rt_low_state = rt_low_state_;
      auto &motor_state = rt_low_state->motor_state;
      // show tau from real time states
      saveData(motor_state[i * kFootNum + j].tau_est, mj_data->time,
               tau_est_figs_[i]->line_data[j]);
    }
    PlotUpdateData(tau_est_figs_[i], mj_data->time);
  }
}

// show lowstate figures
void Agent::PlotShow(mjrRect *rect, mjrContext *con) {
  mjrRect viewport = {rect->left + rect->width - rect->width / 4,
                      rect->height / 4 * 3, rect->width / 4, rect->height / 4};
  if (is_show_custom_figs_) {
    for (auto &fig : joint_figs_) {
      mjr_figure(viewport, fig->fig.get(), con);
      viewport.bottom -= rect->height / 4;
    }
  }
  viewport.bottom = rect->height / 4 * 3;
  if (is_show_tau_figs_) {
    if (is_show_custom_figs_) {
      viewport.left = rect->left + rect->width - rect->width / 2;
    }
    for (auto &fig : tau_figs_) {
      mjr_figure(viewport, fig->fig.get(), con);
      viewport.bottom -= rect->height / 4;
    }
  }

  viewport = {rect->left + rect->width - rect->width / 3, rect->height / 3 * 2,
              rect->width / 3, rect->height / 3};
  if (is_tau_est_) {
    for (auto &fig : tau_est_figs_) {
      mjr_figure(viewport, fig->fig.get(), con);
      viewport.bottom -= rect->height / 3;
    }
  }
}

// reset plot data to zeros
void Agent::PlotResetData(mjvFigure &fig, int index, int length) {
  if (index >= mjMAXLINE) {
    std::cerr << "Too many plots requested: " << index << '\n';
    return;
  }
  int pnt = mjMIN(length, fig.linepnt[index] + 1);

  // shift previous data
  for (int i = pnt - 1; i > 0; i--) {
    fig.linedata[index][2 * i] = 0.0;
    fig.linedata[index][2 * i + 1] = 0.0;
  }

  // current data
  fig.linedata[index][0] = 0.0;
  fig.linedata[index][1] = 0.0;
  fig.linepnt[index] = 0;
  fig.range[0][0] = 0.0;
  fig.range[0][1] = 8.0;
  fig.range[1][0] = -3.0f;
  fig.range[1][1] = 3.0f;
}

// reset plot data to zeros
void Agent::PlotReset() {
  auto line_num = kJointType * 2;
  auto fig_size = joint_figs_.size();
  for (size_t i = 0; i < fig_size; ++i) {
    for (int index = 0; index < line_num; ++index) {
      PlotResetData(*joint_figs_[i]->fig, index, fig_pnt_);
      PlotResetData(*tau_figs_[i]->fig, index, fig_pnt_);
    }
  }

  line_num = kFootNum;
  fig_size = tau_est_figs_.size();
  for (size_t i = 0; i < fig_size; ++i) {
    for (int index = 0; index < line_num; ++index) {
      PlotResetData(*tau_est_figs_[i]->fig, index, fig_pnt_);
    }
  }
  is_first_recv_cmd_.store(false);
  first_cmd_time_ = 0;
}

void Agent::SetSimBeginTime(mjData *mj_Data) {
  if (!is_first_recv_cmd_.load()) {
    first_cmd_time_ = mj_Data->time;
    is_first_recv_cmd_.store(true);
  }
}

void Agent::ShowDepth(mjvScene *scn, mjrContext *con, mjModel *m_,
                      mjrRect *rect) {
  scn->flags[mjtRndFlag::mjRND_SEGMENT] = true;
  scn->flags[mjtRndFlag::mjRND_IDCOLOR] = true;

  // constant width with and without profiler
  int width = is_show_custom_figs_ ? rect->width / 2 : rect->width / 4;

  // render figure in the top right
  mjrRect viewport = {rect->left, rect->bottom, width, rect->height / 4};

  // render scene in offscreen buffer
  mjr_render(viewport, scn, con);

  int total = viewport.width * viewport.height;

  // read depth buffer, values are in range [0, 1]
  std::unique_ptr<float[]> depth(new float[total]);
  mjr_readPixels(nullptr, depth.get(), viewport, con);

  // convert to meters
  float extent = m_->stat.extent;
  float znear = m_->vis.map.znear * extent;
  float zfar = m_->vis.map.zfar * extent;

  auto c_coef = -(zfar + znear) / (zfar - znear);
  auto d_coef = -(2.0 * zfar * znear) / (zfar - znear);

  // In reverse Z mode the perspective matrix is transformed by the following
  c_coef = (-0.5) * c_coef - (0.5);
  d_coef = (-0.5) * d_coef;

  for (int i = 0; i < total; ++i) {
    depth[i] = d_coef / (depth[i] + c_coef);
  }

  float min_depth = std::numeric_limits<float>::max();
  for (int i = 0; i < total; i++) {
    min_depth = std::min(depth[i], min_depth);
  }

  for (int i = 0; i < total; i++) {
    depth[i] -= min_depth;
  }

  float mean = 0;
  float sum = 0;
  int cnt = 0;
  for (int i = 0; i < total; i++) {
    if (depth[i] <= 1) {
      cnt++;
      sum += depth[i];
    }
  }
  mean = sum / cnt;
  for (int i = 0; i < total; i++) {
    depth[i] /= 2.0 * mean;
  }

  // convert to a 3-channel 8-bit image
  std::unique_ptr<unsigned char[]> depth8(
      new unsigned char[3 * viewport.width * viewport.height]);
  for (int i = 0; i < viewport.width * viewport.height; i++) {
    depth8[3 * i] = depth8[3 * i + 1] = depth8[3 * i + 2] = depth[i] * 255.0f;
  }

  // render colorized depth
  glClear(GL_DEPTH_BUFFER_BIT);
  mjr_drawPixels(depth8.get(), nullptr, viewport, con);

  scn->flags[mjtRndFlag::mjRND_SEGMENT] = false;
  scn->flags[mjtRndFlag::mjRND_IDCOLOR] = false;
}

void Agent::ShowCommandId(mjrRect *rect, mjrContext *con) {
  bool in_history = is_load_hist_ || scrub_index_ != 0;
  if (in_history) {
    int i = (history_cursor_ + scrub_index_ + nhistory_) % nhistory_;
    if (!cmd_history_.empty()) {
      last_cmd_id_ = cmd_history_[i];
    }
    if (!state_history_.empty()) {
      last_state_id_ = state_history_[i];
    }
  }

  char text[100];
  if (is_rt_vis_) {
    std::snprintf(text, sizeof(text), "%u(%u)", last_cmd_id_, last_state_id_);
  } else {
    std::snprintf(text, sizeof(text), "%u", last_cmd_id_);
  }
  mjr_overlay(mjFONT_BIG, mjGRID_TOPRIGHT, *rect, text, nullptr, con);
}

void Agent::ApplyForceOnSelBody(mjModel *mj_m, mjData *mj_d) {
  mju_zero(mj_d->qfrc_applied, mj_m->nv);
  if (sel_body_ < 0 || load_weight_ == 0) {
    return;
  }
  mjtNum force[3] = {0, 0, 0};
  mjtNum torque[3] = {0, 0, 0};

  if (load_weight_ > 0) {
    force[2] = load_weight_ * mj_m->opt.gravity[2];
  }
  // transform to global coordinates
  mjtNum gpos[3];
  mju_mulMatVec3(gpos, mj_d->ximat + sel_body_, mj_d->xipos + sel_body_);
  mju_addTo3(gpos, mj_d->qpos);
  // gpos should be in global coordinates
  mj_applyFT(mj_m, mj_d, force, torque, gpos, sel_body_, mj_d->qfrc_applied);
}
void Agent::ShowAppliedForce(mjvScene *scn, mjModel *mj_m, mjData *mj_d) {
  if (sel_body_ < 0 || load_weight_ == 0) {
    return;
  }

  mjtNum frc[3] = {0, 0, load_weight_ * mj_m->opt.gravity[2]};
  mjtNum vec[3];
  mjtNum gpos[3];
  mju_mulMatVec3(gpos, mj_d->ximat + sel_body_, mj_d->xipos + sel_body_);
  mju_addTo3(gpos, mj_d->qpos);

  mjvGeom *thisgeom;
  thisgeom = scn->geoms + scn->ngeom;
  memset(thisgeom, 0, sizeof(mjvGeom));
  mjv_initGeom(thisgeom, mjGEOM_ARROW, nullptr, nullptr, nullptr, nullptr);
  thisgeom->objtype = mjOBJ_UNKNOWN;
  thisgeom->category = mjCAT_DECOR;
  thisgeom->segid = scn->ngeom;

  // draw an arrow representing force
  mju_scl3(vec, frc, mj_m->vis.map.force / mj_m->stat.meanmass);
  mjtNum *from = gpos;
  mjtNum to[3];
  float scl = mj_m->stat.meansize;
  mju_add3(to, from, vec);
  mjv_connector(thisgeom, mjGEOM_ARROW, mj_m->vis.scale.forcewidth * scl, from,
                to);
  memcpy(thisgeom->rgba, mj_m->vis.rgba.force, 4 * sizeof(float));
  scn->ngeom++;
}

}  // namespace vita_sim
