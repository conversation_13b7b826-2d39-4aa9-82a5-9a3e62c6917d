// Copyright 2025 VitaDynamics Limited

#include "vita_sim/info.h"
#include <cstdio>
#include <mujoco/mujoco.h>

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wsign-compare"
#pragma GCC diagnostic ignored "-Wmissing-field-initializers"

#include "simulate/simulate.h"

#pragma GCC diagnostic pop

namespace vita_sim {

void PrintSceneInformation(mujoco::Simulate &sim) {
  // 获取锁以安全访问 model
  const std::lock_guard<std::recursive_mutex> lock(sim.mtx);

  // 检查 model 是否已加载
  if (!sim.m_) {
    printf("Warning: Model not loaded yet\n");
    return;
  }

  printf("\n");

  printf("<<------------- Link ------------->> \n");
  for (int i = 0; i < sim.m_->nbody; i++) {
    const char *name = mj_id2name(sim.m_, mjOBJ_BODY, i);
    if (name) {
      printf("link_index: %d, name: %s\n", i, name);
    }
  }
  printf("\n");

  printf("<<------------- Joint ------------->> \n");
  for (int i = 0; i < sim.m_->njnt; i++) {
    const char *name = mj_id2name(sim.m_, mjOBJ_JOINT, i);
    if (name) {
      printf("joint_index: %d, name: %s\n", i, name);
    }
  }
  printf("\n");

  printf("<<------------- Actuator ------------->> \n");
  for (int i = 0; i < sim.m_->nu; i++) {
    const char *name = mj_id2name(sim.m_, mjOBJ_ACTUATOR, i);
    if (name) {
      printf("actuator_index: %d, name: %s\n", i, name);
    }
  }
  printf("\n");

  printf("<<------------- Sensor ------------->> \n");
  int index = 0;
  // 多维传感器，输出第一维的index
  for (int i = 0; i < sim.m_->nsensor; i++) {
    const char *name = mj_id2name(sim.m_, mjOBJ_SENSOR, i);
    if (name) {
      printf("sensor_index: %d, name: %s, dim: %d\n", index, name,
             sim.m_->sensor_dim[i]);
    }
    index = index + sim.m_->sensor_dim[i];
  }
  printf("\n");
}

} // namespace vita_sim