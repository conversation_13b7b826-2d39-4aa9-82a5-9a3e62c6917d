// Copyright 2025 VitaDynamics Limited

#include "vita_sim/config.h"

#include <filesystem>
#include <functional>
#include <iostream>

#include <ament_index_cpp/get_package_prefix.hpp>
#include <ament_index_cpp/get_package_share_directory.hpp>

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#include <yaml-cpp/yaml.h>
#pragma GCC diagnostic pop

namespace vita_sim {

#define CONFIG_ENTRY(name, type) { \
  #name, [](const YAML::Node &config, SimConfig &sim_config) { \
  assignIfExists(config, #name, sim_config.name); \
}}

template <typename T>
void assignIfExists(const YAML::Node &config, const std::string &key,
                    T &target) {
  if (config[key]) {
    target = config[key].as<T>();
  }
}

SimConfig LoadConfig(const std::string &config_file) {
  std::string path = ament_index_cpp::get_package_share_directory("vita_sim");
  SimConfig sim_config;

  try {
    std::string yaml_path = path + "/" + config_file;
    YAML::Node config = YAML::LoadFile(yaml_path);

    std::unordered_map<std::string,
                       std::function<void(const YAML::Node &, SimConfig &)>>
        configMap = {CONFIG_ENTRY(robot, std::string),
                     CONFIG_ENTRY(robot_scene, std::string),
                     CONFIG_ENTRY(joystick_topic, std::string),
                     CONFIG_ENTRY(low_cmd_topic, std::string),
                     CONFIG_ENTRY(low_state_topic, std::string),
                     CONFIG_ENTRY(rt_low_state_topic, std::string),
                     CONFIG_ENTRY(limit_pause, bool),
                     CONFIG_ENTRY(history_states, int),
                     CONFIG_ENTRY(history_buffer, int),
                     CONFIG_ENTRY(figure_range, float)};

    for (const auto &pair : configMap) {
      pair.second(config, sim_config);
    }
  } catch (const YAML::Exception &e) {
    std::cerr << "Warning: Failed to load config file '" << config_file
              << "': " << e.what() << std::endl;
    std::cerr << "Using default config..." << std::endl;
  }

  return sim_config;
}

std::string ScenePath(const SimConfig &config) {
  std::string path = ament_index_cpp::get_package_share_directory(config.robot);
  return path + "/" + config.robot_scene;
}

void PrintTopic(const SimConfig &config) {
  std::cout << "Joystick topic: " << config.joystick_topic << std::endl;
  std::cout << "Low cmd topic: " << config.low_cmd_topic << std::endl;
  std::cout << "Low state topic: " << config.low_state_topic << std::endl;
}

} // namespace vita_sim
