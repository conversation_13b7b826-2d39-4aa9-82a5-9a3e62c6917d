// Copyright 2025 VitaDynamics Limited

#pragma once

#include <string>

namespace vita_sim {
struct SimConfig {
  std::string robot = "vita00";
  std::string robot_scene = "scene.xml";

  std::string joystick_topic = "/joy";
  std::string low_cmd_topic = "/sim/lowcmd";
  std::string low_state_topic = "/sim/lowstate";
  std::string rt_low_state_topic = "/rt/lowstate";

  bool limit_pause = false;
  int history_states = 10'000;
  int history_buffer = 100'000'000;
  float figure_range = 1.0f;
};

SimConfig LoadConfig(const std::string &config_file);
std::string ScenePath(const SimConfig &config);
void PrintTopic(const SimConfig &config);
} // namespace vita_sim
