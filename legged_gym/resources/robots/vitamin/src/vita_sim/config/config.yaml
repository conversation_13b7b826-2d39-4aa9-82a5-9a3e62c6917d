robot: "vita00"
robot_scene: "scene.xml" # Robot scene, /unitree_robots/[robot]/scene.xml 

joystick_topic: "/joy"
low_cmd_topic: "/sim/lowcmd"      # fsm: "/lowcmd_vita"
low_state_topic: "/sim/lowstate"  # fsm: "rt/lowstate"

rt_low_state_topic: "/rt/lowstate"

limit_pause: false
history_states: 10000     # 存1万个历史状态,步长默认0.002s，即存储历史20s
history_buffer: 100000000 # 存100MB历史数据，与上面那个相比取最小值,vita00模型大概600steps/MB
figure_range: 1.0        # 图表显示的时间跨度，默认绘制1秒的数据
