<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="vita01">
  <mujoco>
    <compiler balanceinertia="true" discardvisual="false" meshdir="../meshes/"/>
  </mujoco>
  <link
    name="base">
    <inertial>
      <origin
        xyz="0.02309712 -0.00142356 0.02843076"
        rpy="0 0 0" />
      <mass
        value="6.71048918" />
      <inertia
        ixx="0.04767060"
        ixy="-0.00032601"
        ixz="-0.04940529"
        iyy="0.21895540"
        iyz="-0.00053286"
        izz="0.19652717" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>  
    <collision>
      <origin rpy="0 0 0" xyz="0.172708 0 0.173335"/>
      <geometry>
        <box size="0.169204 0.090048 0.083746"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0.065416"/>
      <geometry>
        <box size="0.26582 0.16345 0.015322"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.266658 0.165694 0.113034"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.151934 0 0.10129"/>
      <geometry>
        <cylinder length="0.048342" radius="0.025"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.151934 0 0.230308"/>
      <geometry>
        <cylinder length="0.023724" radius="0.025"/>
      </geometry>
    </collision>

    <collision>
      <origin rpy="0 0 0" xyz="0.184451 0 0.064539"/>
      <geometry>
        <box size="0.080206 0.092381 0.010135"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.184451 0 -0.049805"/>
      <geometry>
        <box size="0.080206 0.092381 0.005116"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.247938 0 0.006492"/>
      <geometry>
        <box size="0.011651 0.058022 0.058022"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.636401 0" xyz="0.236863 0 0.050027"/>
      <geometry>
        <box size="0.011651 0.058022 0.02691"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.673517 0" xyz="0.241504 0 -0.036336"/>
      <geometry>
        <box size="0.007721 0.058022 0.016394"/>
      </geometry>
    </collision>

    <collision>
      <origin rpy="0 0 0" xyz="-0.184222 0 0.064539"/>
      <geometry>
        <box size="0.080206 0.092381 0.010135"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="-0.168774 0 -0.049805"/>
      <geometry>
        <box size="0.055263 0.092381 0.005116"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="-0.249278 0 0.006492"/>
      <geometry>
        <box size="0.011651 0.058022 0.047636"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="-3.141593 -0.761086 -3.141593" xyz="-0.238585 0 0.049645"/>
      <geometry>
        <box size="0.011651 0.058022 0.02691"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.920820 0" xyz="-0.224837 0 -0.038571"/>
      <geometry>
        <box size="0.011651 0.058022 0.02691"/>
      </geometry>
    </collision>

    <collision>
      <origin rpy="0 0 0" xyz="0.105417 -0.050388 -0.066185"/>
      <geometry>
        <box size="0.016 0.016 0.013"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.105417 0.050388 -0.066185"/>
      <geometry>
        <box size="0.016 0.016 0.013"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="-0.095043 -0.050388 -0.066185"/>
      <geometry>
        <box size="0.016 0.016 0.013"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="-0.095043 0.050388 -0.066185"/>
      <geometry>
        <box size="0.016 0.016 0.013"/>
      </geometry>
    </collision>

  </link>
  <link
    name="FR_hip">
    <inertial>
      <origin
        xyz="-0.03102627 0.01369255 0.00000000"
        rpy="0 0 0" />
      <mass
        value="0.050966" />
      <inertia
        ixx="0.00000901"
        ixy="-0.00000519"
        ixz="0.00000000"
        iyy="0.00003139"
        iyz="0.00000000"
        izz="0.00003133" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/FR_hip.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="-1.570796 0 0" xyz="0 -0.085705 -0.000177"/>
      <geometry>
        <cylinder length="0.07691" radius="0.044"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="FR_hip_joint"
    type="revolute">
    <origin
      xyz="0.18425 -0.046 0"
      rpy="0 0 0" />
    <parent
      link="base" />
    <child
      link="FR_hip" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.785"
      upper="0.785"
      effort="17"
      velocity="37.7" />
  </joint>
  <link
    name="FR_thigh">
    <inertial>
      <origin
        xyz="-0.00278364 0.04124836 -0.01959991"
        rpy="0 0 0" />
      <mass
        value="1.26607" />
      <inertia
        ixx="0.00443371"
        ixy="-0.00015293"
        ixz="-0.00023733"
        iyy="0.00355986"
        iyz="-0.00103492"
        izz="0.00201292" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/FR_thigh.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.150338 0" xyz="-0.026068 -0.000732 -0.10287"/>
      <geometry>
        <box size="0.025763 0.034784 0.1"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.474394 0" xyz="0.024156 -0.002174 -0.05165"/>
      <geometry>
        <box size="0.001751 0.020706 0.05066"/>
      </geometry>
    </collision>

    <collision>
      <origin rpy="1.570796 0 0" xyz="-0.001819 -0.017832 -0.17896"/>
      <geometry>
        <cylinder length="0.005" radius="0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.570796 0 0" xyz="-0.001819 0.013697 -0.17896"/>
      <geometry>
        <cylinder length="0.005" radius="0.02"/>
      </geometry>
    </collision>

  </link>
  <joint
    name="FR_thigh_joint"
    type="revolute">
    <origin
      xyz="0 -0.089 0"
      rpy="0 0 0" />
    <parent
      link="FR_hip" />
    <child
      link="FR_thigh" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-1.81"
      upper="3.05"
      effort="17"
      velocity="37.7" />
  </joint>
  <link
    name="FR_calf">
    <inertial>
      <origin
        xyz="0.00086705 -0.00012964 -0.11363345"
        rpy="0 0 0" />
      <mass
        value="0.078" />
      <inertia
        ixx="0.00052794"
        ixy="-0.00000001"
        ixz="0.00000850"
        iyy="0.00053258"
        iyz="-0.00000014"
        izz="0.00001005" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/FR_calf.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.109803921568627 0.109803921568627 0.109803921568627 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.052978 0" xyz="0.008882 0 -0.151573"/>
      <geometry>
        <box size="0.019173 0.015288 0.048535"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.128060 0" xyz="0.004891 0 -0.089571"/>
      <geometry>
        <box size="0.016726 0.015288 0.048603"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.203415 0" xyz="-0.005238 0 -0.036777"/>
      <geometry>
        <box size="0.016726 0.015288 0.048603"/>
      </geometry>
    </collision>

  </link>
  <joint
    name="FR_calf_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.17994"
      rpy="0 0 0" />
    <parent
      link="FR_thigh" />
    <child
      link="FR_calf" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.757"
      upper="-0.89"
      effort="34"
      velocity="18.85" />
  </joint>
  <link
    name="FR_foot">
    <inertial>
      <origin
        xyz="-0.00261712 0.00000000 -0.00561277"
        rpy="0 0 0" />
      <mass
        value="0.0158" />
      <inertia
        ixx="0.00000141"
        ixy="0.00000000"
        ixz="0.00000031"
        iyy="0.00000196"
        iyz="0.00000000"
        izz="0.00000193" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/FR_foot.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="-1.570796 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.022"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="FR_foot_joint"
    type="fixed" dont_collapse="true">
    <origin
      xyz="0 0 -0.2"
      rpy="0 0 0" />
    <parent
      link="FR_calf" />
    <child
      link="FR_foot" />
    <axis
      xyz="1 0 0" />
  </joint>
  <link
    name="FL_hip">
    <inertial>
      <origin
        xyz="-0.03102626 -0.01369255 0.00000000"
        rpy="0 0 0" />
      <mass
        value="0.050966" />
      <inertia
        ixx="0.00000901"
        ixy="0.00000519"
        ixz="0.00000000"
        iyy="0.00003139"
        iyz="0.00000000"
        izz="0.00003133" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/FL_hip.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="1.570796 0 0" xyz="0 0.085705 -0.000177"/>
      <geometry>
        <cylinder length="0.07691" radius="0.044"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="FL_hip_joint"
    type="revolute">
    <origin
      xyz="0.18425 0.046 0"
      rpy="0 0 0" />
    <parent
      link="base" />
    <child
      link="FL_hip" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.785"
      upper="0.785"
      effort="17"
      velocity="37.7" />
  </joint>
  <link
    name="FL_thigh">
    <inertial>
      <origin
        xyz="-0.00278364 -0.04124849 -0.01959991"
        rpy="0 0 0" />
      <mass
        value="1.26607" />
      <inertia
        ixx="0.00443371"
        ixy="0.00015293"
        ixz="-0.00023733"
        iyy="0.00355986"
        iyz="0.00103492"
        izz="0.00201292" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/FL_thigh.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.150338 0" xyz="-0.026068 0.000732 -0.10287"/>
      <geometry>
        <box size="0.025763 0.034784 0.1"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.474394 0" xyz="0.024156 0.002174 -0.05165"/>
      <geometry>
        <box size="0.001751 0.020706 0.05066"/>
      </geometry>
    </collision>
  
    <collision>
      <origin rpy="1.570796 0 0" xyz="-0.001819 0.017832 -0.17896"/>
      <geometry>
        <cylinder length="0.005" radius="0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.570796 0 0" xyz="-0.001819 -0.013697 -0.17896"/>
      <geometry>
        <cylinder length="0.005" radius="0.02"/>
      </geometry>
    </collision>

  </link>
  <joint
    name="FL_thigh_joint"
    type="revolute">
    <origin
      xyz="0 0.089 0"
      rpy="0 0 0" />
    <parent
      link="FL_hip" />
    <child
      link="FL_thigh" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-1.81"
      upper="3.05"
      effort="17"
      velocity="37.7" />
  </joint>
  <link
    name="FL_calf">
    <inertial>
      <origin
        xyz="0.00086705 -0.00012964 -0.11363345"
        rpy="0 0 0" />
      <mass
        value="0.078" />
      <inertia
        ixx="0.00052794"
        ixy="0.00000001"
        ixz="0.00000850"
        iyy="0.00053258"
        iyz="0.00000014"
        izz="0.00001005" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/FL_calf.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.109803921568627 0.109803921568627 0.109803921568627 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.052978 0" xyz="0.008882 0 -0.151573"/>
      <geometry>
        <box size="0.019173 0.015288 0.048535"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.128060 0" xyz="0.004891 0 -0.089571"/>
      <geometry>
        <box size="0.016726 0.015288 0.048603"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.203415 0" xyz="-0.005238 0 -0.036777"/>
      <geometry>
        <box size="0.016726 0.015288 0.048603"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="FL_calf_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.17994"
      rpy="0 0 0" />
    <parent
      link="FL_thigh" />
    <child
      link="FL_calf" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.757"
      upper="-0.89"
      effort="34"
      velocity="18.85" />
  </joint>
  <link
    name="FL_foot">
    <inertial>
      <origin
        xyz="-0.00261712 0.00000000 -0.00561277"
        rpy="0 0 0" />
      <mass
        value="0.0158" />
      <inertia
        ixx="0.00000141"
        ixy="0.00000000"
        ixz="0.00000031"
        iyy="0.00000196"
        iyz="0.00000000"
        izz="0.00000193" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/FL_foot.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="-1.570796 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.022"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="FL_foot_joint"
    type="fixed" dont_collapse="true">
    <origin
      xyz="0 0 -0.2"
      rpy="0 0 0" />
    <parent
      link="FL_calf" />
    <child
      link="FL_foot" />
    <axis
      xyz="1 0 0" />
  </joint>

  <link
  name="RR_hip">
  <inertial>
    <origin
      xyz="0.03102627 0.01369255 0.00000000"
      rpy="0 0 0" />
    <mass
      value="0.050966" />
    <inertia
        ixx="0.00000901"
        ixy="0.00000519"
        ixz="0.00000000"
        iyy="0.00003139"
        iyz="0.00000000"
        izz="0.00003133" />
  </inertial>
  <visual>
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <geometry>
      <mesh
        filename="package://vita01/meshes/RR_hip.STL" />
    </geometry>
    <material
      name="">
      <color
        rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
    </material>
  </visual>
  <collision>
    <origin rpy="-1.570796 0 0" xyz="0 -0.085705 -0.000177"/>
    <geometry>
      <cylinder length="0.07691" radius="0.044"/>
    </geometry>
  </collision>
</link>
<joint
  name="RR_hip_joint"
  type="revolute">
  <origin
    xyz="-0.18425 -0.046 0"
    rpy="0 0 0" />
  <parent
    link="base" />
  <child
    link="RR_hip" />
  <axis
    xyz="1 0 0" />
  <limit
    lower="-0.785"
    upper="0.785"
    effort="17"
    velocity="37.7" />
</joint>
<link
  name="RR_thigh">
  <inertial>
    <origin
      xyz="-0.00278364 0.04124836 -0.01959991"
      rpy="0 0 0" />
    <mass
      value="1.26607" />
    <inertia
        ixx="0.00443371"
        ixy="-0.00015293"
        ixz="-0.00023733"
        iyy="0.00355986"
        iyz="-0.00103492"
        izz="0.00201292" />
  </inertial>
  <visual>
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <geometry>
      <mesh
        filename="package://vita01/meshes/RR_thigh.STL" />
    </geometry>
    <material
      name="">
      <color
        rgba="1 1 1 1" />
    </material>
  </visual>
  <collision>
    <origin rpy="0 -0.150338 0" xyz="-0.026068 -0.000732 -0.10287"/>
    <geometry>
      <box size="0.025763 0.034784 0.1"/>
    </geometry>
  </collision>
  <collision>
    <origin rpy="0 0.474394 0" xyz="0.024156 -0.002174 -0.05165"/>
    <geometry>
      <box size="0.001751 0.020706 0.05066"/>
    </geometry>
  </collision>

  <collision>
    <origin rpy="1.570796 0 0" xyz="-0.001819 -0.017832 -0.17896"/>
    <geometry>
      <cylinder length="0.005" radius="0.02"/>
    </geometry>
  </collision>
  <collision>
    <origin rpy="1.570796 0 0" xyz="-0.001819 0.013697 -0.17896"/>
    <geometry>
      <cylinder length="0.005" radius="0.02"/>
    </geometry>
  </collision>

</link>
<joint
  name="RR_thigh_joint"
  type="revolute">
  <origin
    xyz="0 -0.089 0"
    rpy="0 0 0" />
  <parent
    link="RR_hip" />
  <child
    link="RR_thigh" />
  <axis
    xyz="0 1 0" />
  <limit
    lower="-0.75"
    upper="4.13"
    effort="17"
    velocity="37.7" />
</joint>
<link
  name="RR_calf">
  <inertial>
    <origin
      xyz="0.00086705 -0.00012964 -0.11363345"
      rpy="0 0 0" />
    <mass
      value="0.078" />
    <inertia
        ixx="0.00052794"
        ixy="-0.00000001"
        ixz="0.00000850"
        iyy="0.00053258"
        iyz="-0.00000014"
        izz="0.00001005" />
  </inertial>
  <visual>
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <geometry>
      <mesh
        filename="package://vita01/meshes/RR_calf.STL" />
    </geometry>
    <material
      name="">
      <color
        rgba="0.109803921568627 0.109803921568627 0.109803921568627 1" />
    </material>
  </visual>
  <collision>
    <origin rpy="0 -0.052978 0" xyz="0.008882 0 -0.151573"/>
    <geometry>
      <box size="0.019173 0.015288 0.048535"/>
    </geometry>
  </collision>
  <collision>
    <origin rpy="0 -0.128060 0" xyz="0.004891 0 -0.089571"/>
    <geometry>
      <box size="0.016726 0.015288 0.048603"/>
    </geometry>
  </collision>
  <collision>
    <origin rpy="0 -0.203415 0" xyz="-0.005238 0 -0.036777"/>
    <geometry>
      <box size="0.016726 0.015288 0.048603"/>
    </geometry>
  </collision>
</link>
<joint
  name="RR_calf_joint"
  type="revolute">
  <origin
    xyz="0 0 -0.17994"
    rpy="0 0 0" />
  <parent
    link="RR_thigh" />
  <child
    link="RR_calf" />
  <axis
    xyz="0 1 0" />
  <limit
    lower="-2.757"
    upper="-0.89"
    effort="34"
    velocity="18.85" />
</joint>
<link
  name="RR_foot">
  <inertial>
    <origin
      xyz="-0.00261712 0.00000000 -0.00561277"
      rpy="0 0 0" />
    <mass
      value="0.0158" />
    <inertia
        ixx="0.00000141"
        ixy="0.00000000"
        ixz="0.00000031"
        iyy="0.00000196"
        iyz="0.00000000"
        izz="0.00000193" />
  </inertial>
  <visual>
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <geometry>
      <mesh
        filename="package://vita01/meshes/RR_foot.STL" />
    </geometry>
    <material
      name="">
      <color
        rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
    </material>
  </visual>
  <collision>
    <origin rpy="-1.570796 0 0" xyz="0 0 0"/>
    <geometry>
      <sphere radius="0.022"/>
    </geometry>
  </collision>
</link>
<joint
  name="RR_foot_joint"
  type="fixed" dont_collapse="true">
  <origin
    xyz="0 0 -0.2"
    rpy="0 0 0" />
  <parent
    link="RR_calf" />
  <child
    link="RR_foot" />
  <axis
    xyz="1 0 0" />
</joint>

  <link
    name="RL_hip">
    <inertial>
      <origin
        xyz="0.03102626 -0.01369255 0.00000000"
        rpy="0 0 0" />
      <mass
        value="0.050966" />
      <inertia
        ixx="0.00000901"
        ixy="-0.00000519"
        ixz="0.00000000"
        iyy="0.00003139"
        iyz="0.00000000"
        izz="0.00003133" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/RL_hip.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="1.570796 0 0" xyz="0 0.085705 -0.000177"/>
      <geometry>
        <cylinder length="0.07691" radius="0.044"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="RL_hip_joint"
    type="revolute">
    <origin
      xyz="-0.18425 0.046 0"
      rpy="0 0 0" />
    <parent
      link="base" />
    <child
      link="RL_hip" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.785"
      upper="0.785"
      effort="17"
      velocity="37.7" />
  </joint>
  <link
    name="RL_thigh">
    <inertial>
      <origin
        xyz="-0.00278364 -0.04124849 -0.01959991"
        rpy="0 0 0" />
      <mass
        value="1.26607" />
      <inertia
        ixx="0.00443371"
        ixy="0.00015293"
        ixz="-0.00023733"
        iyy="0.00355986"
        iyz="0.00103492"
        izz="0.00201292" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/RL_thigh.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.150338 0" xyz="-0.026068 0.000732 -0.10287"/>
      <geometry>
        <box size="0.025763 0.034784 0.1"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.474394 0" xyz="0.024156 0.002174 -0.05165"/>
      <geometry>
        <box size="0.001751 0.020706 0.05066"/>
      </geometry>
    </collision>
  
    <collision>
      <origin rpy="1.570796 0 0" xyz="-0.001819 0.017832 -0.17896"/>
      <geometry>
        <cylinder length="0.005" radius="0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.570796 0 0" xyz="-0.001819 -0.013697 -0.17896"/>
      <geometry>
        <cylinder length="0.005" radius="0.02"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="RL_thigh_joint"
    type="revolute">
    <origin
      xyz="0 0.089 0"
      rpy="0 0 0" />
    <parent
      link="RL_hip" />
    <child
      link="RL_thigh" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.75"
      upper="4.13"
      effort="17"
      velocity="37.7" />
  </joint>
  <link
    name="RL_calf">
    <inertial>
      <origin
        xyz="0.00086705 -0.00012964 -0.11363345"
        rpy="0 0 0" />
      <mass
        value="0.078" />
      <inertia
        ixx="0.00052794"
        ixy="0.00000001"
        ixz="0.00000850"
        iyy="0.00053258"
        iyz="0.00000014"
        izz="0.00001005" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/RL_calf.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.109803921568627 0.109803921568627 0.109803921568627 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.052978 0" xyz="0.008882 0 -0.151573"/>
      <geometry>
        <box size="0.019173 0.015288 0.048535"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.128060 0" xyz="0.004891 0 -0.089571"/>
      <geometry>
        <box size="0.016726 0.015288 0.048603"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.203415 0" xyz="-0.005238 0 -0.036777"/>
      <geometry>
        <box size="0.016726 0.015288 0.048603"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="RL_calf_joint"
    type="revolute">
    <origin
      xyz="0 0 -0.17994"
      rpy="0 0 0" />
    <parent
      link="RL_thigh" />
    <child
      link="RL_calf" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.757"
      upper="-0.89"
      effort="34"
      velocity="18.85" />
  </joint>
  <link
    name="RL_foot">
    <inertial>
      <origin
        xyz="-0.00261712 0.00000000 -0.00561277"
        rpy="0 0 0" />
      <mass
        value="0.0158" />
      <inertia
        ixx="0.00000141"
        ixy="0.00000000"
        ixz="0.00000031"
        iyy="0.00000196"
        iyz="0.00000000"
        izz="0.00000193" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vita01/meshes/RL_foot.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="-1.570796 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.022"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="RL_foot_joint"
    type="fixed" dont_collapse="true">
    <origin
      xyz="0 0 -0.2"
      rpy="0 0 0" />
    <parent
      link="RL_calf" />
    <child
      link="RL_foot" />
    <axis
      xyz="1 0 0" />
  </joint>
</robot>