cmake_minimum_required(VERSION 3.8)
project(vita_odom)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 定义宏选项，默认启用VITA_MSG
option(VITA_MSG "Use lowlevel_msg instead of unitree_go" OFF)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(Eigen3 REQUIRED)

# 根据宏选项选择依赖包
if(VITA_MSG)
  find_package(lowlevel_msg REQUIRED)
  add_compile_definitions(VITA_MSG)
  message(STATUS "Using lowlevel_msg package")
  set(MSG_DEPENDENCY lowlevel_msg)
else()
  find_package(unitree_go REQUIRED)
  message(STATUS "Using unitree_go package")
  set(MSG_DEPENDENCY unitree_go)
endif()

message(STATUS "Eigen3_INCLUDE_DIR: ${EIGEN3_INCLUDE_DIR}")

# 使用绝对路径确保clangd能找到头文件
include_directories(
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${EIGEN3_INCLUDE_DIR}
)

# 创建IMU Odometry节点
add_executable(imu_odometry_node
  src/imu_odometry_node.cpp
  src/imu_odometry.cpp
  src/unitree_estimator.cpp
)

# 为目标添加更明确的包含目录
target_include_directories(imu_odometry_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)

ament_target_dependencies(imu_odometry_node
  rclcpp
  sensor_msgs
  nav_msgs
  geometry_msgs
  tf2
  tf2_ros
  tf2_geometry_msgs
  Eigen3
  ${MSG_DEPENDENCY}
)

# 安装目标
install(TARGETS
  imu_odometry_node
  DESTINATION lib/${PROJECT_NAME}
)

# 安装头文件
install(DIRECTORY
  include/
  DESTINATION include
)

# 安装启动脚本
install(DIRECTORY
  launch
  DESTINATION share/${PROJECT_NAME}
)

ament_package()