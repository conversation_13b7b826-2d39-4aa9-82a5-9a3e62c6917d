# vita_odom

基于IMU和足部接触状态的四足机器人里程计节点，适用于Unitree Go2和Vita机器人。

## 功能介绍

- 基于IMU数据和足部接触状态进行里程计计算
- 基于Unitree的Go2Estimator卡尔曼滤波算法
- 接收LowState消息，提取IMU数据和足部接触力
- 发布标准ROS2 Odometry消息和TF变换

## 依赖

- ROS2
- Eigen3
- tf2/tf2_ros
- unitree_go / lowlevel_msg 消息包
- nav_msgs, geometry_msgs

## 编译

```bash
# 在工作空间根目录
colcon build --packages-select vita_odom
```

## 运行

使用启动文件:

```bash
ros2 launch vita_odom imu_odom.launch.py
```

可配置的启动参数:

- `world_frame_id`: 世界坐标系ID (默认: "unitree_odom")
- `base_frame_id`: 机器人基座坐标系ID (默认: "base_link_imu")
- `input_topic`: 输入话题名称 (默认: "/lowstate")
- `output_topic`: 输出里程计话题名称 (默认: "/rt/odom")

例如，使用仿真环境:

```bash
ros2 launch vita_odom imu_odom.launch.py input_topic:=/sim/lowstate output_topic:=/sim/odom
```

## 数据流

**输入**:
- LowState消息 (`/lowstate`)，包含IMU数据和足部接触力数据

**输出**:
- 里程计消息 (默认: `/rt/odom`)
- TF变换 (从 `world_frame_id` 到 `base_frame_id`)

## 实现原理

该节点直接使用Unitree提供的状态估计器对IMU和足部接触状态进行融合，计算机器人的位置和姿态。它将足部力传感器数据转换为接触状态，通过卡尔曼滤波处理传感器噪声，提供平滑的位置、速度和姿态输出。

当足部接触地面时，算法会降低对应位置分量的方差，从而使估计更稳定；当足部悬空时，则更多依赖IMU积分。这种算法比单纯的IMU积分更能抑制漂移。

## 注意事项

- 足部力阈值默认设为40.0，可能需要根据机器人实际情况调整
- 当前只支持Unitree Go2/Vita的LowState消息格式
- 代码仍在开发中，可能存在稳定性问题 