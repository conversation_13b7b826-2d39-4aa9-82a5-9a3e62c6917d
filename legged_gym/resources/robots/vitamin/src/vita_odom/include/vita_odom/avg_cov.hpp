// Copyright 2025 VitaDynamics Limited

#pragma once

#include <string>
#include <vector>

#include <Eigen/Dense>

namespace vita_odom {

/**
 * @brief 协方差均值计算类
 */
class AvgCov {
public:
  /**
   * @brief 构造函数
   * @param dim 维度
   * @param name 名称
   */
  AvgCov(int dim, const std::string &name = "") : dim_(dim), name_(name) {
    current_mean_ = Eigen::MatrixXd::Zero(dim, dim);
    count_ = 0;
  }

  /**
   * @brief 添加新的协方差矩阵
   * @param new_cov 新的协方差矩阵
   */
  void addCov(const Eigen::MatrixXd &new_cov) {
    if (count_ == 0) {
      current_mean_ = new_cov;
    } else {
      current_mean_ = (current_mean_ * count_ + new_cov) / (count_ + 1);
    }
    ++count_;
  }

  /**
   * @brief 获取当前均值
   * @return 均值协方差矩阵
   */
  const Eigen::MatrixXd &getMean() const { return current_mean_; }

  /**
   * @brief 获取维度
   * @return 维度
   */
  int getDim() const { return dim_; }

  /**
   * @brief 获取采样数量
   * @return 采样数量
   */
  int getCount() const { return count_; }

  /**
   * @brief 获取名称
   * @return 名称
   */
  std::string getName() const { return name_; }

private:
  int dim_;                      // 维度
  std::string name_;             // 名称
  Eigen::MatrixXd current_mean_; // 当前均值
  int count_;                    // 采样数量
};

/**
 * @brief 低通滤波器
 */
class LPFilter {
public:
  /**
   * @brief 构造函数
   * @param dt 时间步长
   * @param cutoff 截止频率
   */
  LPFilter(double dt, double cutoff) {
    alpha_ = dt / (dt + 1.0 / (2.0 * M_PI * cutoff));
    value_ = 0.0;
  }

  /**
   * @brief 添加新值
   * @param new_value 新值
   * @return 滤波后的值
   */
  double addValue(double new_value) {
    value_ = alpha_ * new_value + (1.0 - alpha_) * value_;
    return value_;
  }

  /**
   * @brief 获取当前值
   * @return 当前值
   */
  double getValue() const { return value_; }

private:
  double alpha_; // 滤波器参数
  double value_; // 当前值
};

} // namespace vita_odom
