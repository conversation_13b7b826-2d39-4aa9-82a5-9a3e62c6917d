// Copyright 2025 VitaDynamics Limited

#pragma once

#include "vita_odom/unitree_types.hpp"

namespace vita_odom {

/**
 * @brief IMU状态结构体
 */
struct IMUState {
  double quaternion[4] = {1.0, 0.0, 0.0, 0.0}; // 单位四元数 [w, x, y, z]
  double gyroscope[3] = {0.0, 0.0, 0.0};       // 角速度
  double accelerometer[3] = {0.0, 0.0, 0.0};   // 加速度计
  double rpy[3] = {0.0, 0.0, 0.0};             // 欧拉角 [roll, pitch, yaw]
  double temperature = 0.0;                    // 温度

  /**
   * @brief 获取加速度向量
   * @return 加速度向量
   */
  Vec3 getAcc() const {
    return Vec3(accelerometer[0], accelerometer[1], accelerometer[2]);
  }

  /**
   * @brief 获取角速度向量
   * @return 角速度向量
   */
  Vec3 getGyro() const {
    return Vec3(gyroscope[0], gyroscope[1], gyroscope[2]);
  }

  /**
   * @brief 获取欧拉角向量
   * @return 欧拉角向量
   */
  Vec3 getRpy() const { return Vec3(rpy[0], rpy[1], rpy[2]); }
};

/**
 * @brief 低级别状态类
 */
class LowlevelState {
public:
  IMUState imu;    // IMU数据
  Vec34 joint_q_;  // 关节角度
  Vec34 joint_qd_; // 关节角速度

  /**
   * @brief 获取旋转矩阵
   * @return 从体坐标系到全局坐标系的旋转矩阵
   */
  Mat3 getRotMat() const {
    double w = imu.quaternion[0];
    double x = imu.quaternion[1];
    double y = imu.quaternion[2];
    double z = imu.quaternion[3];

    Mat3 R;
    R << 1 - 2 * y * y - 2 * z * z, 2 * x * y - 2 * w * z,
        2 * x * z + 2 * w * y, 2 * x * y + 2 * w * z, 1 - 2 * x * x - 2 * z * z,
        2 * y * z - 2 * w * x, 2 * x * z - 2 * w * y, 2 * y * z + 2 * w * x,
        1 - 2 * x * x - 2 * y * y;

    return R;
  }

  /**
   * @brief 获取关节角度矩阵
   * @return 关节角度矩阵
   */
  Vec34 getQ() const { return joint_q_; }

  /**
   * @brief 获取关节角速度矩阵
   * @return 关节角速度矩阵
   */
  Vec34 getQd() const { return joint_qd_; }

  /**
   * @brief 更新来自SportModeState的IMU数据
   * @param quaternion 四元数
   * @param gyroscope 角速度
   * @param accelerometer 加速度
   */
  void updateIMU(const double quaternion[4], const double gyroscope[3],
                 const double accelerometer[3]) {
    for (int i = 0; i < 4; i++) {
      imu.quaternion[i] = quaternion[i];
    }

    for (int i = 0; i < 3; i++) {
      imu.gyroscope[i] = gyroscope[i];
      imu.accelerometer[i] = accelerometer[i];
    }
  }

  template <typename MotorState>
  void UpdateJointState(const MotorState &motor) {
    for (int i = 0; i < 4; i++) {
      joint_q_.col(i) =
          Vec3(motor[i * 3].q, motor[i * 3 + 1].q, motor[i * 3 + 2].q);
      joint_qd_.col(i) =
          Vec3(motor[i * 3].dq, motor[i * 3 + 1].dq, motor[i * 3 + 2].dq);
    }
  }
};

} // namespace vita_odom
