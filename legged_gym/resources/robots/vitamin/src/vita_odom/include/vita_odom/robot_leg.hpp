// Copyright 2025 VitaDynamics Limited

#pragma once

#include <cmath>
#include <iostream>

#include "vita_odom/unitree_types.hpp"

namespace vita_odom {

/**
 * @brief 四足机器人腿部基类
 */
class QuadrupedLeg {
public:
  /**
   * @brief 构造函数
   * @param legID 腿的ID(0:FR, 1:FL, 2:RR, 3:RL)
   * @param abadLinkLength 髋关节偏移量
   * @param hipLinkLength 大腿长度
   * @param kneeLinkLength 小腿长度
   * @param pHip2B 髋关节相对于机身的位置
   */
  QuadrupedLeg(int legID, float abadLinkLength, float hipLinkLength,
               float kneeLinkLength, Vec3 pHip2B)
      : abadLinkLength_(abadLinkLength), hipLinkLength_(hipLinkLength),
        kneeLinkLength_(kneeLinkLength), pHip2B_(pHip2B) {
    if (legID == 0 || legID == 2) {
      sideSign_ = -1; // 右腿
    } else if (legID == 1 || legID == 3) {
      sideSign_ = 1; // 左腿
    } else {
      std::cerr << "Leg ID incorrect!" << std::endl;
      exit(-1);
    }
  }

  /**
   * @brief 计算足端相对于髋关节的位置(正向运动学)
   * @param q 关节角度
   * @return 足端相对于髋关节的位置
   */
  Vec3 calcPEe2H(Vec3 q) {
    float l1 = sideSign_ * abadLinkLength_;
    float l2 = -hipLinkLength_;
    float l3 = -kneeLinkLength_;

    float s1 = std::sin(q(0));
    float s2 = std::sin(q(1));
    float s3 = std::sin(q(2));

    float c1 = std::cos(q(0));
    float c2 = std::cos(q(1));
    float c3 = std::cos(q(2));

    float c23 = c2 * c3 - s2 * s3;
    float s23 = s2 * c3 + c2 * s3;

    Vec3 pEe2H;

    pEe2H(0) = l3 * s23 + l2 * s2;
    pEe2H(1) = -l3 * s1 * c23 + l1 * c1 - l2 * c2 * s1;
    pEe2H(2) = l3 * c1 * c23 + l1 * s1 + l2 * c1 * c2;

    return pEe2H;
  }

  /**
   * @brief 计算足端相对于机身的位置(正向运动学)
   * @param q 关节角度
   * @return 足端相对于机身的位置
   */
  Vec3 calcPEe2B(Vec3 q) { return pHip2B_ + calcPEe2H(q); }

  /**
   * @brief 计算足端速度(导数正向运动学)
   * @param q 关节角度
   * @param qd 关节角速度
   * @return 足端速度
   */
  Vec3 calcVEe(Vec3 q, Vec3 qd) { return calcJaco(q) * qd; }

  /**
   * @brief 计算关节角度(逆向运动学)
   * @param pEe 足端位置
   * @param frame 参考坐标系
   * @return 关节角度
   */
  Vec3 calcQ(Vec3 pEe, FrameType frame) {
    Vec3 pEe2H;
    if (frame == FrameType::HIP) {
      pEe2H = pEe;
    } else if (frame == FrameType::BODY) {
      pEe2H = pEe - pHip2B_;
    } else {
      std::cerr
          << "[ERROR] The frame of QuadrupedLeg::calcQ can only be HIP or BODY!"
          << std::endl;
      exit(-1);
    }

    float q1, q2, q3;
    Vec3 qResult;
    float px, py, pz;
    float b2y, b3z, b4z, a, b, c;

    px = pEe2H(0);
    py = pEe2H(1);
    pz = pEe2H(2);

    b2y = abadLinkLength_ * sideSign_;
    b3z = -hipLinkLength_;
    b4z = -kneeLinkLength_;
    a = abadLinkLength_;
    c = sqrt(pow(px, 2) + pow(py, 2) + pow(pz, 2)); // 整体长度
    b = sqrt(pow(c, 2) - pow(a, 2));                // 肩部到足端的距离

    q1 = q1Ik(py, pz, b2y);
    q3 = q3Ik(b3z, b4z, b);
    q2 = q2Ik(q1, q3, px, py, pz, b3z, b4z);

    qResult(0) = q1;
    qResult(1) = q2;
    qResult(2) = q3;

    return qResult;
  }

  /**
   * @brief 计算关节角速度(导数逆向运动学)
   * @param q 关节角度
   * @param vEe 足端速度
   * @return 关节角速度
   */
  Vec3 calcQd(Vec3 q, Vec3 vEe) { return calcJaco(q).inverse() * vEe; }

  /**
   * @brief 计算关节角速度(导数逆向运动学)
   * @param pEe 足端位置
   * @param vEe 足端速度
   * @param frame 参考坐标系
   * @return 关节角速度
   */
  Vec3 calcQd(Vec3 pEe, Vec3 vEe, FrameType frame) {
    Vec3 q = calcQ(pEe, frame);
    return calcJaco(q).inverse() * vEe;
  }

  /**
   * @brief 计算关节力矩(逆向动力学)
   * @param q 关节角度
   * @param force 足端力
   * @return 关节力矩
   */
  Vec3 calcTau(Vec3 q, Vec3 force) { return calcJaco(q).transpose() * force; }

  /**
   * @brief 计算雅可比矩阵
   * @param q 关节角度
   * @return 雅可比矩阵
   */
  Mat3 calcJaco(Vec3 q) {
    Mat3 jaco;

    float l1 = abadLinkLength_ * sideSign_;
    float l2 = -hipLinkLength_;
    float l3 = -kneeLinkLength_;

    float s1 = std::sin(q(0));
    float s2 = std::sin(q(1));
    float s3 = std::sin(q(2));

    float c1 = std::cos(q(0));
    float c2 = std::cos(q(1));
    float c3 = std::cos(q(2));

    float c23 = c2 * c3 - s2 * s3;
    float s23 = s2 * c3 + c2 * s3;

    jaco(0, 0) = 0;
    jaco(1, 0) = -l3 * c1 * c23 - l2 * c1 * c2 - l1 * s1;
    jaco(2, 0) = -l3 * s1 * c23 - l2 * c2 * s1 + l1 * c1;
    jaco(0, 1) = l3 * c23 + l2 * c2;
    jaco(1, 1) = l3 * s1 * s23 + l2 * s1 * s2;
    jaco(2, 1) = -l3 * c1 * s23 - l2 * c1 * s2;
    jaco(0, 2) = l3 * c23;
    jaco(1, 2) = l3 * s1 * s23;
    jaco(2, 2) = -l3 * c1 * s23;

    return jaco;
  }

private:
  /**
   * @brief 计算第一个关节角度(髋关节横摆)
   * @param py y方向位置
   * @param pz z方向位置
   * @param l1 第一个连杆长度
   * @return 关节角度
   */
  float q1Ik(float py, float pz, float l1) {
    float q1;
    float L = sqrt(pow(py, 2) + pow(pz, 2) - pow(l1, 2));
    q1 = atan2(pz * l1 + py * L, py * l1 - pz * L);
    return q1;
  }

  /**
   * @brief 计算第三个关节角度(膝关节)
   * @param b3z 大腿长度
   * @param b4z 小腿长度
   * @param b 肩部到足端距离
   * @return 关节角度
   */
  float q3Ik(float b3z, float b4z, float b) {
    float q3, temp;
    temp = (pow(b3z, 2) + pow(b4z, 2) - pow(b, 2)) / (2 * fabs(b3z * b4z));

    if (temp > 1)
      temp = 1;
    if (temp < -1)
      temp = -1;

    q3 = acos(temp);
    q3 = -(M_PI - q3); // 0~180
    return q3;
  }

  /**
   * @brief 计算第二个关节角度(大腿摆动)
   * @param q1 第一个关节角度
   * @param q3 第三个关节角度
   * @param px x方向位置
   * @param py y方向位置
   * @param pz z方向位置
   * @param b3z 大腿长度
   * @param b4z 小腿长度
   * @return 关节角度
   */
  float q2Ik(float q1, float q3, float px, float py, float pz, float b3z,
             float b4z) {
    float q2, a1, a2, m1, m2;

    a1 = py * sin(q1) - pz * cos(q1);
    a2 = px;
    m1 = b4z * sin(q3);
    m2 = b3z + b4z * cos(q3);
    q2 = atan2(m1 * a1 + m2 * a2, m1 * a2 - m2 * a1);
    return q2;
  }

protected:
  float abadLinkLength_; // 髋关节偏移量
  float hipLinkLength_;  // 大腿长度
  float kneeLinkLength_; // 小腿长度
  Vec3 pHip2B_;          // 髋关节相对于机身的位置
  int sideSign_;         // 左/右腿标记(1: 左, -1: 右)
};

/**
 * @brief Go2机器人腿部类
 */
class Go2Leg : public QuadrupedLeg {
public:
  /**
   * @brief 构造函数
   * @param legID 腿的ID(0:FR, 1:FL, 2:RR, 3:RL)
   * @param pHip2B 髋关节相对于机身的位置
   */
  Go2Leg(int legID, Vec3 pHip2B)
      : QuadrupedLeg(legID, 0.0955, 0.213, 0.213, pHip2B) {
    // 从Go2的URDF中提取参数
    // 髋关节偏移: 0.0955 (从URDF中的FL_thigh_joint/FR_thigh_joint的y偏移获取)
    // 大腿长度: 0.213 (从URDF中的FL_calf_joint的z偏移获取)
    // 小腿长度: 0.213 (从URDF中的FL_foot_joint的z偏移获取)
  }
};

/**
 * @brief Vita00机器人腿部类
 */
class Vita00Leg : public QuadrupedLeg {
public:
  /**
   * @brief 构造函数
   * @param legID 腿的ID(0:FR, 1:FL, 2:RR, 3:RL)
   * @param pHip2B 髋关节相对于机身的位置
   */
  Vita00Leg(int legID, Vec3 pHip2B)
      : QuadrupedLeg(legID, 0.0893, 0.22, 0.22, pHip2B) {
    // 从Vita00的URDF中提取参数
    // 髋关节偏移: 0.0893 (从URDF中的FL_thigh_joint/FR_thigh_joint的y偏移获取)
    // 大腿长度: 0.22 (从URDF中的FL_calf_joint的z偏移获取)
    // 小腿长度: 0.22 (从URDF中的FL_foot_joint的z偏移获取)
  }
};

} // namespace vita_odom
