// Copyright 2025 VitaDynamics Limited

#pragma once

#include <memory>
#include <string>

#include "vita_odom/avg_cov.hpp"
#include "vita_odom/lowlevel_state.hpp"
#include "vita_odom/robot_model.hpp"
#include "vita_odom/unitree_types.hpp"

namespace vita_odom {

/**
 * @brief 状态估计器类
 */
class Estimator {
public:
  /**
   * @brief 构造函数
   * @param robot_model 机器人模型
   * @param low_state 低级别状态
   * @param contact 接触状态
   * @param phase 相位
   * @param dt 时间步长
   * @param Qdig 过程噪声对角线
   * @param est_name 估计器名称
   */
  Estimator(QuadrupedRobot *robot_model, LowlevelState *low_state,
            VecInt4 *contact, Vec4 *phase, double dt, Vec18 Qdig,
            std::string est_name);

  /**
   * @brief 构造函数(使用默认参数)
   * @param robot_model 机器人模型
   * @param low_state 低级别状态
   * @param contact 接触状态
   * @param phase 相位
   * @param dt 时间步长
   */
  Estimator(QuadrupedRobot *robot_model, LowlevelState *low_state,
            VecInt4 *contact, Vec4 *phase, double dt);

  /**
   * @brief 析构函数
   */
  ~Estimator();

  /**
   * @brief 初始化系统
   */
  void initSystem();

  /**
   * @brief 运行估计器
   */
  void run();

  /**
   * @brief 获取位置
   * @return 位置向量
   */
  Vec3 getPosition();

  /**
   * @brief 获取速度
   * @return 速度向量
   */
  Vec3 getVelocity();

  /**
   * @brief 获取足端位置
   * @param i 腿ID
   * @return 足端位置
   */
  Vec3 getFootPos(int i);

  /**
   * @brief 获取所有足端位置
   * @return 足端位置矩阵
   */
  Vec34 getFeetPos();

  /**
   * @brief 获取所有足端速度
   * @return 足端速度矩阵
   */
  Vec34 getFeetVel();

  /**
   * @brief 获取足端相对于机身的全局位置
   * @return 足端位置矩阵
   */
  Vec34 getPosFeet2BGlobal();

private:
  QuadrupedRobot *robot_model_; // 机器人模型
  LowlevelState *low_state_;    // 低级别状态
  VecInt4 *contact_;            // 接触状态
  Vec4 *phase_;                 // 相位
  double dt_;                   // 时间步长
  Vec18 Qdig_;                  // 过程噪声对角线
  std::string est_name_;        // 估计器名称

  Vec3 g_;                // 重力向量
  double large_variance_; // 大方差值

  // 状态估计相关变量
  Vec18 xhat_;                     // 状态估计
  Vec3 u_;                         // 控制输入
  Mat18 A_;                        // 状态转移矩阵
  Eigen::Matrix<double, 18, 3> B_; // 控制输入矩阵
  Mat28_18 C_;                     // 测量矩阵
  Mat18 P_;                        // 协方差矩阵
  Mat18 Q_;                        // 过程噪声矩阵
  Eigen::MatrixXd R_;              // 测量噪声矩阵
  Eigen::MatrixXd RInit_;          // 初始测量噪声矩阵
  Mat18 QInit_;                    // 初始过程噪声矩阵
  Mat3 Cu_;                        // 控制噪声相关

  // 滤波器相关变量
  Eigen::MatrixXd Ppriori_;                  // 先验协方差
  Eigen::MatrixXd S_;                        // 系统创新协方差
  Eigen::PartialPivLU<Eigen::MatrixXd> Slu_; // LU分解
  Eigen::VectorXd Sy_;                       // 系统创新
  Eigen::MatrixXd Sc_;                       // 系统创新
  Eigen::MatrixXd SR_;                       // 系统创新
  Eigen::MatrixXd STC_;                      // 系统创新
  Eigen::MatrixXd IKC_;                      // 状态估计更新

  // 足部相关变量
  Vec4 feetH_;              // 足部高度
  Vec12 feetPos2Body_;      // 足部位置
  Vec12 feetVel2Body_;      // 足部速度
  Vec34 feetPosGlobalKine_; // 足部位置全局运动学
  Vec34 feetVelGlobalKine_; // 足部速度全局运动学

  Mat3 rotMatB2G_; // 从体坐标系到全局坐标系的旋转矩阵

  double trust_; // 信任度

  // 协方差检查
  AvgCov *RCheck_; // 测量噪声检查
  AvgCov *uCheck_; // 控制输入检查

  // 速度滤波器
  LPFilter *vxFilter_; // X方向速度滤波器
  LPFilter *vyFilter_; // Y方向速度滤波器
  LPFilter *vzFilter_; // Z方向速度滤波器

  // 测量
  Eigen::VectorXd y_;    // 测量
  Eigen::VectorXd yhat_; // 预测测量
};

/**
 * @brief Go2状态估计器类
 */
class Go2Estimator : public Estimator {
public:
  /**
   * @brief 构造函数
   * @param low_state 低级别状态
   * @param contact 接触状态
   * @param phase 相位
   * @param dt 时间步长
   */
  Go2Estimator(LowlevelState *low_state, VecInt4 *contact, Vec4 *phase,
               double dt);
};

/**
 * @brief Vita00状态估计器类
 */
class Vita00Estimator : public Estimator {
public:
  /**
   * @brief 构造函数
   * @param low_state 低级别状态
   * @param contact 接触状态
   * @param phase 相位
   * @param dt 时间步长
   */
  Vita00Estimator(LowlevelState *low_state, VecInt4 *contact, Vec4 *phase,
                  double dt);
};

} // namespace vita_odom
