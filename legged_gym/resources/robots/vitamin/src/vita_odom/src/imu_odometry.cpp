// Copyright 2025 VitaDynamics Limited

#include "vita_odom/imu_odometry.hpp"
#include "vita_odom/unitree_estimator.hpp"

#include <array>
#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <utility>

#include <Eigen/Dense>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

namespace vita_odom {

ImuOdometry::ImuOdometry() : Node("imu_odometry") {
  // 声明和获取参数
  declare_parameter("world_frame_id", "unitree_odom");
  declare_parameter("base_frame_id", "base_link_imu");

  world_frame_id_ = get_parameter("world_frame_id").as_string();
  base_frame_id_ = get_parameter("base_frame_id").as_string();

  // 初始化协方差数组 - 直接从unitree_guide复用
  pose_covariance_ = {1e-9, 0, 0,   0, 0,   0, 0, 1e-3, 1e-9, 0,   0, 0,
                      0,    0, 1e6, 0, 0,   0, 0, 0,    0,    1e6, 0, 0,
                      0,    0, 0,   0, 1e6, 0, 0, 0,    0,    0,   0, 1e-9};

  twist_covariance_ = {1e-9, 0, 0,   0, 0,   0, 0, 1e-3, 1e-9, 0,   0, 0,
                       0,    0, 1e6, 0, 0,   0, 0, 0,    0,    1e6, 0, 0,
                       0,    0, 0,   0, 1e6, 0, 0, 0,    0,    0,   0, 1e-9};

  // 初始化ROS2相关
  tf_broadcaster_ = std::make_shared<tf2_ros::TransformBroadcaster>(this);

  // 创建发布者和订阅者
  rclcpp::QoS qos_profile(500);
  qos_profile.history(rclcpp::HistoryPolicy::KeepAll);

  odom_pub_ =
      create_publisher<nav_msgs::msg::Odometry>("/rt/odom", qos_profile);
  low_state_sub_ = create_subscription<LowStateMsg>(
      "/lowstate", qos_profile,
      std::bind(&ImuOdometry::LowStateCallback, this, std::placeholders::_1));

// 初始化Unitree状态估计器相关
#ifdef VITA_MSG
  robot_model_ = std::make_unique<Vita00Robot>();
#else
  robot_model_ = std::make_unique<Go2Robot>();
#endif
  low_state_ = std::make_unique<LowlevelState>();
  contact_state_.setZero();
  phase_.setZero();

  // 创建状态估计器
  double dt = 1.0 / 500.0; // 假设IMU数据为500Hz
#ifdef VITA_MSG
  estimator_ = std::make_unique<Vita00Estimator>(low_state_.get(),
                                                 &contact_state_, &phase_, dt);
#else
  estimator_ = std::make_unique<Go2Estimator>(low_state_.get(), &contact_state_,
                                              &phase_, dt);
#endif
}

ImuOdometry::~ImuOdometry() {}

using Vec34 = Eigen::Matrix<double, 3, 4>;

void ImuOdometry::LowStateCallback(const typename LowStateMsg::SharedPtr msg) {
  rclcpp::Time current_time = this->get_clock()->now();

  // 从imu_state字段提取IMU数据
  const auto &imu_state = msg->imu_state;

  // 更新低级别状态中的IMU数据
  double quaternion[4] = {imu_state.quaternion[0], imu_state.quaternion[1],
                          imu_state.quaternion[2], imu_state.quaternion[3]};

  double gyroscope[3] = {imu_state.gyroscope[0], imu_state.gyroscope[1],
                         imu_state.gyroscope[2]};

  double accelerometer[3] = {imu_state.accelerometer[0],
                             imu_state.accelerometer[1],
                             imu_state.accelerometer[2]};

  low_state_->updateIMU(quaternion, gyroscope, accelerometer);
  low_state_->UpdateJointState(msg->motor_state);

  // 更新足部接触状态
  UpdateFootContactState(msg->foot_force);

  // 运行状态估计
  estimator_->run();

  // 直接发布里程计数据，确保时间戳与输入数据一致
  PublishOdometry(current_time);
}

void ImuOdometry::UpdateFootContactState(
    const std::array<int16_t, 4> &foot_forces) {
  // 使用足部力传感器数据判断接触状态
  // foot_forces 顺序: FR(0), FL(1), RR(2), RL(3)

  // 使用简单的滞后比较器判断接触状态
  const int16_t force_threshold_high = 25; // 接触阈值上限
  const int16_t force_threshold_low = 10;  // 接触阈值下限

  // 状态持久变量
  static bool prev_contact[4] = {false, false, false, false};

  // 基于滞后比较器(hysteresis comparator)原理更新接触状态
  for (int i = 0; i < 4; i++) {
    if (foot_forces[i] > force_threshold_high) {
      // 力超过上阈值，确认为接触状态
      prev_contact[i] = true;
    } else if (foot_forces[i] < force_threshold_low) {
      // 力低于下阈值，确认为非接触状态
      prev_contact[i] = false;
    }
    // 否则保持之前的状态，不改变

    // 更新接触状态
    contact_state_(i) = prev_contact[i] ? 1 : 0;
  }

  // 更新相位(phase)以便状态估计器使用
  for (int i = 0; i < 4; i++) {
    phase_(i) = contact_state_(i) ? 0.0 : 0.5;
  }
}

void ImuOdometry::PublishOdometry(const rclcpp::Time &current_time) {
  // 获取状态估计器的结果
  Vec3 position = estimator_->getPosition();
  Vec3 velocity = estimator_->getVelocity();

  // 填充TF数据
  odom_tf_.header.stamp = current_time;
  odom_tf_.header.frame_id = world_frame_id_;
  odom_tf_.child_frame_id = base_frame_id_;

  odom_tf_.transform.translation.x = position(0);
  odom_tf_.transform.translation.y = position(1);
  odom_tf_.transform.translation.z = position(2);

  odom_tf_.transform.rotation.w = low_state_->imu.quaternion[0];
  odom_tf_.transform.rotation.x = low_state_->imu.quaternion[1];
  odom_tf_.transform.rotation.y = low_state_->imu.quaternion[2];
  odom_tf_.transform.rotation.z = low_state_->imu.quaternion[3];

  // 发布TF
  tf_broadcaster_->sendTransform(odom_tf_);

  // 填充里程计消息
  odom_msg_.header.stamp = current_time;
  odom_msg_.header.frame_id = world_frame_id_;
  odom_msg_.child_frame_id = base_frame_id_;

  // 设置位姿
  odom_msg_.pose.pose.position.x = position(0);
  odom_msg_.pose.pose.position.y = position(1);
  odom_msg_.pose.pose.position.z = position(2);

  odom_msg_.pose.pose.orientation.w = low_state_->imu.quaternion[0];
  odom_msg_.pose.pose.orientation.x = low_state_->imu.quaternion[1];
  odom_msg_.pose.pose.orientation.y = low_state_->imu.quaternion[2];
  odom_msg_.pose.pose.orientation.z = low_state_->imu.quaternion[3];

  // 设置速度
  // 将全局坐标系下的速度转换到机器人坐标系
  Mat3 R = low_state_->getRotMat();
  Vec3 vel_body = R.transpose() * velocity;

  odom_msg_.twist.twist.linear.x = vel_body(0);
  odom_msg_.twist.twist.linear.y = vel_body(1);
  odom_msg_.twist.twist.linear.z = vel_body(2);

  odom_msg_.twist.twist.angular.x = low_state_->imu.gyroscope[0];
  odom_msg_.twist.twist.angular.y = low_state_->imu.gyroscope[1];
  odom_msg_.twist.twist.angular.z = low_state_->imu.gyroscope[2];

  // 设置协方差
  for (size_t i = 0; i < 36; ++i) {
    odom_msg_.pose.covariance[i] = pose_covariance_[i];
    odom_msg_.twist.covariance[i] = twist_covariance_[i];
  }

  // 发布里程计消息
  odom_pub_->publish(odom_msg_);
}

} // namespace vita_odom