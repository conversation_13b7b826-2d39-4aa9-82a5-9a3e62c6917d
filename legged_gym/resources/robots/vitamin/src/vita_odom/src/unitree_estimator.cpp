// Copyright 2025 VitaDynamics Limited

#include "vita_odom/unitree_estimator.hpp"

#include <chrono>
#include <cmath>
#include <iostream>

namespace vita_odom {

Estimator::Estimator(QuadrupedRobot *robot_model, LowlevelState *low_state,
                     VecInt4 *contact, Vec4 *phase, double dt, Vec18 Qdig,
                     std::string est_name)
    : robot_model_(robot_model), low_state_(low_state), contact_(contact),
      phase_(phase), dt_(dt), Qdig_(Qdig), est_name_(est_name) {
  initSystem();
}

Estimator::Estimator(QuadrupedRobot *robot_model, LowlevelState *low_state,
                     VecInt4 *contact, Vec4 *phase, double dt)
    : robot_model_(robot_model), low_state_(low_state), contact_(contact),
      phase_(phase), dt_(dt) {
  // 设置默认的过程噪声
  Qdig_ = Vec18::Zero();
  for (int i(0); i < Qdig_.rows(); ++i) {
    if (i < 3) {
      Qdig_(i) = 0.0003;
    } else if (i < 6) {
      Qdig_(i) = 0.0003;
    } else {
      Qdig_(i) = 0.01;
    }
  }

  est_name_ = "current";
  initSystem();
}

Estimator::~Estimator() {
  delete RCheck_;
  delete uCheck_;
  delete vxFilter_;
  delete vyFilter_;
  delete vzFilter_;
}

void Estimator::initSystem() {
  // 初始化重力向量和大方差值
  g_ << 0, 0, -9.81;
  large_variance_ = 100;

  // 初始化状态向量和控制输入
  xhat_.setZero();
  u_.setZero();

  // 初始化状态转移矩阵
  A_.setZero();
  A_.block(0, 0, 3, 3) = I3;
  A_.block(0, 3, 3, 3) = I3 * dt_;
  A_.block(3, 3, 3, 3) = I3;
  A_.block(6, 6, 12, 12) = I12;

  // 初始化控制输入矩阵
  B_.setZero();
  B_.block(3, 0, 3, 3) = I3 * dt_;

  // 初始化测量矩阵
  C_.setZero();
  C_.block(0, 0, 3, 3) = -I3;
  C_.block(3, 0, 3, 3) = -I3;
  C_.block(6, 0, 3, 3) = -I3;
  C_.block(9, 0, 3, 3) = -I3;
  C_.block(12, 3, 3, 3) = -I3;
  C_.block(15, 3, 3, 3) = -I3;
  C_.block(18, 3, 3, 3) = -I3;
  C_.block(21, 3, 3, 3) = -I3;
  C_.block(0, 6, 12, 12) = I12;
  C_(24, 8) = 1;
  C_(25, 11) = 1;
  C_(26, 14) = 1;
  C_(27, 17) = 1;

  // 初始化协方差矩阵
  P_.setIdentity();
  P_ = large_variance_ * P_;

  // 初始化测量噪声矩阵
  RInit_ = Eigen::MatrixXd::Identity(28, 28);
  // 增加对足端位置测量的信任度
  for (int i = 0; i < 12; ++i) {
    RInit_(i, i) = 0.01;
  }
  // 增加对速度测量的信任度
  for (int i = 12; i < 24; ++i) {
    RInit_(i, i) = 0.01;
  }

  // A1/Go1/Go2 噪声特性
  Cu_ << 268.573, -43.819, -147.211, -43.819, 92.949, 58.082, -147.211, 58.082,
      302.120;

  // 初始化过程噪声矩阵
  QInit_ = Qdig_.asDiagonal();
  QInit_ += B_ * Cu_ * B_.transpose();

  // 初始化噪声检查
  RCheck_ = new AvgCov(28, est_name_ + " R");
  uCheck_ = new AvgCov(3, est_name_ + " u");

  // 初始化速度滤波器
  vxFilter_ = new LPFilter(dt_, 3.0);
  vyFilter_ = new LPFilter(dt_, 3.0);
  vzFilter_ = new LPFilter(dt_, 3.0);

  // 初始化测量向量
  y_ = Eigen::VectorXd::Zero(28);
  yhat_ = Eigen::VectorXd::Zero(28);

  // 初始化滤波器变量
  Ppriori_ = Eigen::MatrixXd::Zero(18, 18);
  S_ = Eigen::MatrixXd::Zero(28, 28);
  Sy_ = Eigen::VectorXd::Zero(28);
  Sc_ = Eigen::MatrixXd::Zero(28, 18);
  SR_ = Eigen::MatrixXd::Zero(28, 28);
  STC_ = Eigen::MatrixXd::Zero(18, 28);
  IKC_ = Eigen::MatrixXd::Zero(18, 18);

  // 初始化足部变量
  feetH_.setZero();
  feetPos2Body_ = Eigen::VectorXd::Zero(12);
  feetVel2Body_ = Eigen::VectorXd::Zero(12);
}

void Estimator::run() {
  // 处理足部相关数据
  feetH_.setZero();
  feetPosGlobalKine_ =
      robot_model_->getFeet2BPositions(*low_state_, FrameType::GLOBAL);
  feetVelGlobalKine_ =
      robot_model_->getFeet2BVelocities(*low_state_, FrameType::GLOBAL);

  // 设置系统噪声
  Q_ = QInit_;
  R_ = RInit_;

  // 根据足部接触状态调整系统噪声
  for (int i(0); i < 4; ++i) {
    if ((*contact_)(i) == 0) {
      // 未接触时增大方差
      Q_.block(6 + 3 * i, 6 + 3 * i, 3, 3) = large_variance_ * I3;
      R_.block(12 + 3 * i, 12 + 3 * i, 3, 3) = large_variance_ * I3;
      R_(24 + i, 24 + i) = large_variance_;
    } else {
      // 接触时根据相位平滑过渡
      // 调用windowFunc模板函数，提供所有必要参数
      trust_ = windowFunc<double>((*phase_)(i), 0.2);
      Q_.block(6 + 3 * i, 6 + 3 * i, 3, 3) =
          (1 + (1 - trust_) * large_variance_) *
          QInit_.block(6 + 3 * i, 6 + 3 * i, 3, 3);
      R_.block(12 + 3 * i, 12 + 3 * i, 3, 3) =
          (1 + (1 - trust_) * large_variance_) *
          RInit_.block(12 + 3 * i, 12 + 3 * i, 3, 3);
      R_(24 + i, 24 + i) =
          (1 + (1 - trust_) * large_variance_) * RInit_(24 + i, 24 + i);
    }
    // 存储足部位置和速度
    feetPos2Body_.segment(3 * i, 3) = feetPosGlobalKine_.col(i);
    feetVel2Body_.segment(3 * i, 3) = feetVelGlobalKine_.col(i);
  }

  // 获取当前姿态和加速度
  rotMatB2G_ = low_state_->getRotMat();
  u_ = rotMatB2G_ * low_state_->imu.getAcc() + g_;

  // 状态预测
  xhat_ = A_ * xhat_ + B_ * u_;
  yhat_ = C_ * xhat_;

  // 构建测量向量
  y_ << feetPos2Body_, feetVel2Body_, feetH_;

  // 卡尔曼滤波计算
  Ppriori_ = A_ * P_ * A_.transpose() + Q_;
  S_ = R_ + C_ * Ppriori_ * C_.transpose();
  Slu_ = S_.lu();
  Sy_ = Slu_.solve(y_ - yhat_);
  Sc_ = Slu_.solve(C_);
  SR_ = Slu_.solve(R_);
  STC_ = S_.transpose().lu().solve(C_);
  IKC_ = Eigen::MatrixXd::Identity(18, 18) - Ppriori_ * C_.transpose() * Sc_;

  // 状态更新
  xhat_ += Ppriori_ * C_.transpose() * Sy_;
  P_ = IKC_ * Ppriori_ * IKC_.transpose() +
       Ppriori_ * C_.transpose() * SR_ * STC_ * Ppriori_.transpose();

  // 速度滤波
  vxFilter_->addValue(xhat_(3));
  vyFilter_->addValue(xhat_(4));
  vzFilter_->addValue(xhat_(5));
}

Vec3 Estimator::getPosition() { return xhat_.segment(0, 3); }

Vec3 Estimator::getVelocity() { return xhat_.segment(3, 3); }

Vec3 Estimator::getFootPos(int i) {
  return getPosition() +
         low_state_->getRotMat() *
             robot_model_->getFootPosition(*low_state_, i, FrameType::BODY);
}

Vec34 Estimator::getFeetPos() {
  Vec34 feetPos;
  for (int i(0); i < 4; ++i) {
    feetPos.col(i) = getFootPos(i);
  }
  return feetPos;
}

Vec34 Estimator::getFeetVel() {
  Vec34 feetVel =
      robot_model_->getFeet2BVelocities(*low_state_, FrameType::GLOBAL);
  for (int i(0); i < 4; ++i) {
    feetVel.col(i) += getVelocity();
  }
  return feetVel;
}

Vec34 Estimator::getPosFeet2BGlobal() {
  Vec34 feet2BPos;
  for (int i(0); i < 4; ++i) {
    feet2BPos.col(i) = getFootPos(i) - getPosition();
  }
  return feet2BPos;
}

Go2Estimator::Go2Estimator(LowlevelState *low_state, VecInt4 *contact,
                           Vec4 *phase, double dt)
    : Estimator(new Go2Robot(), low_state, contact, phase, dt) {
  // Go2特定的初始化 - 如果需要的话
}

Vita00Estimator::Vita00Estimator(LowlevelState *low_state, VecInt4 *contact,
                                 Vec4 *phase, double dt)
    : Estimator(new Vita00Robot(), low_state, contact, phase, dt) {
  // Vita00特定的初始化 - 如果需要的话
}

} // namespace vita_odom