{"configById": {"DogLegSideView.DogLegSideView!2n3c7iq": {"lowStateTopic": "/rt/lowstate", "lowCmdTopic": "/rl_lowcmd", "robotType": "vita00"}, "RawMessages!1a537f6": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rl_lowcmd", "fontSize": 12}, "RawMessages!2kwfjxn": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rt/lowstate", "fontSize": 12}, "Plot!4jizk9q": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[$motor].q", "enabled": false, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[$motor].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[$motor].tau_est", "enabled": false, "color": "#f7df71", "label": "sim.tau"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[$motor].dq", "enabled": true, "color": "#4e98e2", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[$motor].q", "enabled": true, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[$motor].q", "enabled": true, "color": "#61cbff", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[$motor].tau_est", "enabled": false, "color": "#a395e2", "label": "real.state.tau"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[$motor].dq", "enabled": false, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "GlobalVariableSliderPanel!3nulfcx": {"sliderProps": {"min": 0, "max": 11, "step": 1}, "globalVariableName": "motor"}, "RawMessages!iyhoq9": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rl_lowcmd.cmd_id", "fontSize": 36}, "RawMessages!23k4q7q": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rt/lowstate.state_id", "fontSize": 36}, "Plot!20dw5kh": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[0].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[0].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[0].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[0].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[0].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[0].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[0].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FR-Hip"}, "Plot!fayhsv": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[3].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[3].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[3].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[3].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[3].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[3].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[3].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FL-Hip"}, "Plot!23qf7n7": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[6].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[6].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[6].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[6].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[6].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[6].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[6].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RR-Hip"}, "Plot!45osfc7": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[9].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[9].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[9].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[9].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[9].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[9].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[9].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RL-Hip"}, "Plot!1x3ukec": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[1].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[1].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[1].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[1].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[1].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[1].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[1].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FR-Thigh"}, "Plot!2h0yztc": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[4].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[4].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[4].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[4].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[4].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[4].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[4].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FL-Thigh"}, "Plot!1i09j0d": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[7].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[7].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[7].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[7].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[7].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[7].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[7].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RR-Thigh"}, "Plot!wknpua": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[10].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[10].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[10].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[10].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[10].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[10].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[10].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RL-Thigh"}, "Plot!14zausu": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[2].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[2].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[2].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[2].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[2].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[2].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[2].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FR-Calf"}, "Plot!vf3eu": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[5].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[5].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[5].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[5].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[5].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[5].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[5].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FL-Calf"}, "Plot!3in8hf1": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[8].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[8].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[8].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[8].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[8].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[8].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[8].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RR-Calf"}, "Plot!q23nkn": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[11].q", "enabled": true, "color": "#4e98e2", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[11].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[11].q", "enabled": false, "color": "#f7df71", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[11].q", "enabled": true, "color": "#5cd6a9", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[11].q", "enabled": true, "color": "#61cbff", "label": "real.raw.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[11].tau_est", "enabled": true, "color": "#a395e2", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[11].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RL-Calf"}, "RawMessages!358xmf5": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "", "fontSize": 12}, "RawMessages!3i8nnmj": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "", "fontSize": 12}, "RawMessages!36s4opb": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "", "fontSize": 12}, "RawMessages!1iopdih": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "", "fontSize": 12}, "Tab!3hlxzed": {"activeTabIdx": 2, "tabs": [{"title": "Visualize", "layout": {"first": "DogLegSideView.DogLegSideView!2n3c7iq", "second": {"first": "RawMessages!1a537f6", "second": "RawMessages!2kwfjxn", "direction": "column"}, "direction": "row", "splitPercentage": 64.39894319682959}}, {"title": "Analysis", "layout": {"first": "Plot!4jizk9q", "second": {"first": "GlobalVariableSliderPanel!3nulfcx", "second": {"first": "RawMessages!iyhoq9", "second": "RawMessages!23k4q7q", "direction": "row"}, "direction": "row", "splitPercentage": 88.81373569198752}, "direction": "column", "splitPercentage": 83.35607094133697}}, {"title": "Dashboard", "layout": {"first": {"first": {"first": "Plot!20dw5kh", "second": "Plot!fayhsv", "direction": "column"}, "second": {"first": "Plot!23qf7n7", "second": "Plot!45osfc7", "direction": "column"}, "direction": "column"}, "second": {"first": {"first": {"first": "Plot!1x3ukec", "second": "Plot!2h0yztc", "direction": "column"}, "second": {"first": "Plot!1i09j0d", "second": "Plot!wknpua", "direction": "column"}, "direction": "column"}, "second": {"first": {"first": "Plot!14zausu", "second": "Plot!vf3eu", "direction": "column"}, "second": {"first": "Plot!3in8hf1", "second": "Plot!q23nkn", "direction": "column"}, "direction": "column"}, "direction": "row"}, "direction": "row", "splitPercentage": 31.047265987025025}}, {"title": "Message", "layout": {"first": {"first": "RawMessages!358xmf5", "second": "RawMessages!3i8nnmj", "direction": "row"}, "second": {"first": "RawMessages!36s4opb", "second": "RawMessages!1iopdih", "direction": "row"}, "direction": "column", "splitPercentage": 47.92243767313019}}]}}, "globalVariables": {"motor": 0}, "userNodes": {}, "playbackConfig": {"speed": 1}, "layout": "Tab!3hlxzed"}