FROM ros:humble-ros-core-jammy AS ros_base_builder

# install bootstrap tools
RUN apt-get update && apt-get install --no-install-recommends -y \
    build-essential \
    git \
    libgl1 libegl-dev \
    python3.10 \
    python3-pip \
    python3-rosdep \
    && pip install uv \
    && rm -rf /var/lib/apt/lists/*

# bootstrap rosdep
RUN rosdep init && \
  rosdep update --rosdistro $ROS_DISTRO

RUN apt-get update && apt-get install -y --no-install-recommends \
  ros-humble-ros-base=0.10.0-1* \
  && rm -rf /var/lib/apt/lists/*

FROM ros_base_builder AS vitasim_builder
WORKDIR /root/vitasim_cloud
RUN mkdir -p mcap models
COPY src/robot models/
ENV MUJOCO_GL=EGL
COPY install install

FROM vitasim_builder
COPY tools/offscreen offscreen
WORKDIR /root/vitasim_cloud/offscreen
RUN uv sync
